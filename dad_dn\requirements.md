# 📄 requirements.md – Prayer & Fasting Tracker App + Flutter Boilerplate Features

## 🧩 Overview  
Flutter-приложение для отслеживания пропущенных намазов и постов, уведомлений о времени намаза, духовного журнала, чтения Корана, тасбих-счётчика и социальной ленты пользователей.  
Используется масштабируемый Flutter boilerplate с аутентификацией, маршрутизацией, state management, пуш-уведомлениями и безопасным хранением данных.

---

## 🔑 Key Features

### 🕌 Missed Prayer & Fasting Tracker  
- Ввод даты рождения и пола пользователя.  
- Расчёт даты наступления обязанности совершать религиозные обряды (мужчины – 12 лет, женщины – 9 лет).  
- Автоматический подсчёт пропущенных намазов и постов по текущей дате.  
- Чекбоксы для отметки совершённых/компенсированных (кадха) намазов и постов.  
- Счётчик пропущенных обязанностей обновляется в реальном времени.

### ⏰ Prayer Time Notifications  
- Автоматическое определение локации или ручной ввод.  
- Расписание намазов: Фаджр, Зухр, Аср, Магриб, Иша.  
- Локальные уведомления с кастомным звуком и вибрацией.  
- Поддержка исламского (хиджри) календаря.

### 📖 Quran Module  
- Полный Коран (арабский текст + перевод).  
- Поиск по суре и аяту.  
- Закладки для сохранения прочитанного.  
- Ежедневное напоминание с аятом.

### 🔢 Tasbih Counter  
- Цифровой тасбих с настраиваемыми зикрами.  
- Сброс и сохранение счёта.  
- Поддержка нескольких наборов тасбиха.

### 🧕 User Profile  
- Имя, пол, дата рождения, мазхаб (опционально).  
- Отслеживание общего количества совершённых кадха.  
- Статистика последнего входа, использования приложения и истории тасбиха.

### 🌍 Community (Ummah Feed)  
- Публикация исламских размышлений (текст, фото, видео).  
- Лайки, комментарии, сохранение постов.  
- Управление приватностью ленты (публичная/приватная).

---

## ⚙️ Core Boilerplate Features

### Authentication  
- Email/пароль, OAuth (Google, Apple, GitHub).  
- Биометрия (Touch ID / Face ID).  
- Безопасное хранение и обновление токенов.  
- Поддержка Firebase Auth или Clerk.com.

### Routing  
- `go_router` или `auto_route` с защитой маршрутов.  
- Deep linking и динамические параметры маршрутов.

### State Management  
- Riverpod (или Bloc) с разделением UI, логики и данных.  
- Асинхронная обработка с состояниями загрузки и ошибок.

### Settings  
- Переключение темы (светлая/тёмная).  
- Многоязычность (i18n-ready).  
- Настройки уведомлений.  
- Информация о версии и проверка обновлений.

### Push Notifications  
- Firebase Cloud Messaging (FCM).  
- Уведомления в разных состояниях приложения.  
- Локальные уведомления с поддержкой мультимедиа.

---

## 🔒 Privacy & Data  
- Безопасное хранение пользовательских данных (`flutter_secure_storage`).  
- Облачная синхронизация через Firebase (опционально).  
- Анонимный вход (guest login).

---

## 🧪 Developer Experience & Extras  
- Тестирование (unit, widget, integration).  
- CI/CD (GitHub Actions / Codemagic).  
- Отслеживание ошибок (Sentry / Firebase Crashlytics).  
- Модульная архитектура, адаптивный дизайн, офлайн-кэширование.  
- Code generation (freezed, json_serializable).

---

## ⚙️ Optional Advanced Features  
- Встроенная библиотека дуа.  
- Аудио Коран с рецитаторами.  
- Календарь и планировщик поста Рамадан.  
- Система стриков для мотивации.  
- Трекер настроения и дневник.
