import 'package:flutter/material.dart';

class CommunityPage extends StatelessWidget {
  const CommunityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ummah Feed'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // TODO: Create new post
            },
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Community Feed',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            
            // Sample Posts
            Expanded(
              child: ListView(
                children: [
                  PostCard(
                    username: '<PERSON>',
                    timeAgo: '2 hours ago',
                    content: '<PERSON><PERSON><PERSON><PERSON><PERSON> for another blessed day. May Allah guide us all on the straight path.',
                    likes: 15,
                    comments: 3,
                  ),
                  <PERSON><PERSON><PERSON><PERSON>(height: 16),
                  PostCard(
                    username: '<PERSON><PERSON>',
                    timeAgo: '4 hours ago',
                    content: 'Just finished reading <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, the stories are so inspiring!',
                    likes: 23,
                    comments: 7,
                  ),
                  <PERSON><PERSON><PERSON><PERSON>(height: 16),
                  Post<PERSON>ard(
                    username: '<PERSON>',
                    timeAgo: '1 day ago',
                    content: 'Reminder: "And whoever relies upon Allah - then He is sufficient for him. Indeed, Allah will accomplish His purpose." (Quran 65:3)',
                    likes: 45,
                    comments: 12,
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 16),
            Text(
              'Features to implement:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text('• Post Islamic reflections (text, photo, video)'),
            Text('• Like and comment system'),
            Text('• Save posts'),
            Text('• Privacy controls'),
            Text('• Content moderation'),
          ],
        ),
      ),
    );
  }
}

class PostCard extends StatelessWidget {
  final String username;
  final String timeAgo;
  final String content;
  final int likes;
  final int comments;

  const PostCard({
    super.key,
    required this.username,
    required this.timeAgo,
    required this.content,
    required this.likes,
    required this.comments,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const CircleAvatar(
                  child: Icon(Icons.person),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        username,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        timeAgo,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: () {
                    // TODO: Show post options
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              content,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.favorite_border),
                  onPressed: () {
                    // TODO: Like post
                  },
                ),
                Text('$likes'),
                const SizedBox(width: 16),
                IconButton(
                  icon: const Icon(Icons.comment_outlined),
                  onPressed: () {
                    // TODO: Show comments
                  },
                ),
                Text('$comments'),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.bookmark_border),
                  onPressed: () {
                    // TODO: Save post
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
