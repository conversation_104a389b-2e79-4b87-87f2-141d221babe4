import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:async';

void main() {
  runApp(const DadDnApp());
}

class DadDnApp extends StatelessWidget {
  const DadDnApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Dad Dn - Prayer & Fasting Tracker',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2E7D32),
        ),
      ),
      home: const HomePage(),
    );
  }
}

// Data models
class PrayerData {
  final String name;
  final String arabicName;
  final String time; // Prayer time
  bool isCompleted;

  PrayerData({
    required this.name,
    required this.arabicName,
    required this.time,
    this.isCompleted = false,
  });
}

class FastingData {
  final String type;
  final DateTime date;
  bool isCompleted;

  FastingData({
    required this.type,
    required this.date,
    this.isCompleted = false,
  });
}

// Ramadan dates data (real Islamic calendar dates)
class RamadanData {
  static final Map<int, Map<String, dynamic>> ramadanDates = {
    2020: {'start': DateTime(2020, 4, 24), 'end': DateTime(2020, 5, 23)},
    2021: {'start': DateTime(2021, 4, 13), 'end': DateTime(2021, 5, 12)},
    2022: {'start': DateTime(2022, 4, 2), 'end': DateTime(2022, 5, 1)},
    2023: {'start': DateTime(2023, 3, 23), 'end': DateTime(2023, 4, 21)},
    2024: {'start': DateTime(2024, 3, 11), 'end': DateTime(2024, 4, 9)},
    2025: {'start': DateTime(2025, 2, 28), 'end': DateTime(2025, 3, 29)},
    2026: {'start': DateTime(2026, 2, 17), 'end': DateTime(2026, 3, 18)},
    2027: {'start': DateTime(2027, 2, 6), 'end': DateTime(2027, 3, 7)},
    2028: {'start': DateTime(2028, 1, 26), 'end': DateTime(2028, 2, 24)},
    2029: {'start': DateTime(2029, 1, 15), 'end': DateTime(2029, 2, 13)},
    2030: {'start': DateTime(2030, 1, 5), 'end': DateTime(2030, 2, 3)},
  };

  static List<DateTime> getRamadanDays(int year) {
    final ramadan = ramadanDates[year];
    if (ramadan == null) return [];

    List<DateTime> days = [];
    DateTime current = ramadan['start'];
    DateTime end = ramadan['end'];

    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      days.add(current);
      current = current.add(const Duration(days: 1));
    }

    return days;
  }

  static bool isRamadanYear(int year) {
    return ramadanDates.containsKey(year);
  }
}

// Storage for prayer/fasting data by date
class DailyData {
  static final Map<String, Map<String, bool>> _prayerData = {};
  static final Map<String, Map<String, bool>> _fastingData = {};

  static String _dateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  static bool getPrayerStatus(DateTime date, String prayerName) {
    final key = _dateKey(date);
    return _prayerData[key]?[prayerName] ?? false;
  }

  static void setPrayerStatus(DateTime date, String prayerName, bool status) {
    final key = _dateKey(date);
    _prayerData[key] ??= {};
    _prayerData[key]![prayerName] = status;
  }

  static bool getFastingStatus(DateTime date, String fastingType) {
    final key = _dateKey(date);
    return _fastingData[key]?[fastingType] ?? false;
  }

  static void setFastingStatus(DateTime date, String fastingType, bool status) {
    final key = _dateKey(date);
    _fastingData[key] ??= {};
    _fastingData[key]![fastingType] = status;
  }
}

class UserProfile {
  final DateTime birthDate;
  final String gender;
  final DateTime obligationStartDate;

  UserProfile({
    required this.birthDate,
    required this.gender,
  }) : obligationStartDate = _calculateObligationStartDate(birthDate, gender);

  static DateTime _calculateObligationStartDate(DateTime birthDate, String gender) {
    int obligationAge = gender == 'male' ? 12 : 9;
    return DateTime(
      birthDate.year + obligationAge,
      birthDate.month,
      birthDate.day,
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  late List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [
      const PrayerTrackerPage(),
      const PrayerTimesPage(),
      const QuranPage(),
      const TasbihPage(),
      const CommunityPage(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF2E7D32),
              Color(0xFF4CAF50),
              Color(0xFF81C784),
            ],
          ),
        ),
        child: _pages[_selectedIndex],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _selectedIndex,
          onTap: (index) => setState(() => _selectedIndex = index),
          backgroundColor: Colors.transparent,
          selectedItemColor: const Color(0xFF2E7D32),
          unselectedItemColor: Colors.grey[600],
          elevation: 0,
          selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
          unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal),
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.mosque),
                  label: 'Prayer Tracker',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.access_time),
                  label: 'Prayer Times',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.menu_book),
                  label: 'Quran',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.radio_button_checked),
                  label: 'Tasbih',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.people),
                  label: 'Community',
                ),
              ],
            ),
        ),
    );
  }
}

// Prayer Tracker Page with full functionality
class PrayerTrackerPage extends StatefulWidget {
  const PrayerTrackerPage({super.key});

  @override
  State<PrayerTrackerPage> createState() => _PrayerTrackerPageState();
}

class _PrayerTrackerPageState extends State<PrayerTrackerPage> {
  DateTime selectedDate = DateTime.now();
  UserProfile? userProfile;
  DateTime currentTime = DateTime.now();
  StreamSubscription<DateTime>? timeSubscription;
  bool isRamadanExpanded = false;

  @override
  void initState() {
    super.initState();
    userProfile = UserProfile(
      birthDate: DateTime(2000, 1, 1),
      gender: 'male',
    );

    // Real-time clock stream
    timeSubscription = Stream.periodic(const Duration(seconds: 1), (_) => DateTime.now())
        .listen((time) {
      if (mounted) {
        setState(() {
          currentTime = time;
        });
      }
    });
  }

  @override
  void dispose() {
    // Cancel any active streams/timers
    timeSubscription?.cancel();
    super.dispose();
  }

  List<PrayerData> get prayers {
    return [
      PrayerData(name: 'Fajr', arabicName: 'الفجر', time: '05:30'),
      PrayerData(name: 'Dhuhr', arabicName: 'الظهر', time: '12:15'),
      PrayerData(name: 'Asr', arabicName: 'العصر', time: '15:45'),
      PrayerData(name: 'Maghrib', arabicName: 'المغرب', time: '18:20'),
      PrayerData(name: 'Isha', arabicName: 'العشاء', time: '19:50'),
    ].map((prayer) {
      prayer.isCompleted = DailyData.getPrayerStatus(selectedDate, prayer.name);
      return prayer;
    }).toList();
  }

  // Removed fasting getter - now handled by Ramadan details

  int get totalMissedPrayers {
    if (userProfile == null) return 0;

    DateTime startDate = userProfile!.obligationStartDate;
    DateTime endDate = selectedDate;

    if (endDate.isBefore(startDate)) return 0;

    int daysDiff = endDate.difference(startDate).inDays;
    return daysDiff * 5;
  }

  int get totalMissedFasting {
    if (userProfile == null) return 0;

    DateTime startDate = userProfile!.obligationStartDate;
    DateTime currentDate = DateTime.now();

    if (currentDate.isBefore(startDate)) return 0;

    int yearsDiff = currentDate.year - startDate.year;
    return yearsDiff * 30;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('Prayer Tracker'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () => _showProfileDialog(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Real-time clock and statistics
              _buildGlassCard(
                child: Column(
                  children: [
                    Text(
                      '${currentTime.hour.toString().padLeft(2, '0')}:${currentTime.minute.toString().padLeft(2, '0')}:${currentTime.second.toString().padLeft(2, '0')}',
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      '${currentTime.day}/${currentTime.month}/${currentTime.year}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem('Missed Prayers', totalMissedPrayers.toString()),
                        _buildStatItem('Missed Fasting', totalMissedFasting.toString()),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Date Navigation
              _buildGlassCard(
                child: Column(
                  children: [
                    const Text(
                      'Select Date',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              selectedDate = selectedDate.subtract(const Duration(days: 1));
                            });
                          },
                          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                        ),
                        GestureDetector(
                          onTap: () => _selectDate(),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: selectedDate.isBefore(DateTime.now()) ? () {
                            setState(() {
                              selectedDate = selectedDate.add(const Duration(days: 1));
                            });
                          } : null,
                          icon: const Icon(Icons.arrow_forward_ios, color: Colors.white),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              _buildGlassCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Missed Prayers',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...prayers.map((prayer) => _buildPrayerCheckbox(prayer)),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              _buildGlassCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Ramadan Fasting',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              isRamadanExpanded = !isRamadanExpanded;
                            });
                          },
                          icon: Icon(
                            isRamadanExpanded ? Icons.expand_less : Icons.expand_more,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    if (!isRamadanExpanded) ...[
                      _buildRamadanSummary(),
                    ],
                    if (isRamadanExpanded) ...[
                      _buildRamadanDetails(),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGlassCard({required Widget child}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: child,
        ),
      ),
    );
  }

  Widget _buildPrayerCheckbox(PrayerData prayer) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Checkbox(
            value: prayer.isCompleted,
            onChanged: (value) {
              setState(() {
                prayer.isCompleted = value ?? false;
                DailyData.setPrayerStatus(selectedDate, prayer.name, prayer.isCompleted);
              });
            },
            activeColor: Colors.white,
            checkColor: const Color(0xFF2E7D32),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      prayer.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      prayer.time,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  prayer.arabicName,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRamadanSummary() {
    if (userProfile == null) return const SizedBox();

    DateTime obligationStart = userProfile!.obligationStartDate;
    DateTime currentDate = DateTime.now();

    int totalRamadanYears = 0;
    int completedRamadanYears = 0;

    for (int year = obligationStart.year; year <= currentDate.year; year++) {
      if (RamadanData.isRamadanYear(year)) {
        totalRamadanYears++;
        if (DailyData.getFastingStatus(DateTime(year, 1, 1), 'Ramadan$year')) {
          completedRamadanYears++;
        }
      }
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'Total Ramadan Years',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
          Text(
            '$completedRamadanYears / $totalRamadanYears',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: userProfile?.obligationStartDate ?? DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  void _showProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('User Profile'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Birth Date: ${userProfile?.birthDate.day}/${userProfile?.birthDate.month}/${userProfile?.birthDate.year}'),
            Text('Gender: ${userProfile?.gender}'),
            Text('Obligation Start: ${userProfile?.obligationStartDate.day}/${userProfile?.obligationStartDate.month}/${userProfile?.obligationStartDate.year}'),
            const SizedBox(height: 16),
            Text('Total Missed Prayers: $totalMissedPrayers'),
            Text('Total Missed Fasting: $totalMissedFasting'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildRamadanDetails() {
    if (userProfile == null) return const SizedBox();

    DateTime obligationStart = userProfile!.obligationStartDate;
    DateTime currentDate = DateTime.now();

    // Generate Ramadan years from obligation start to current year
    List<int> ramadanYears = [];
    for (int year = obligationStart.year; year <= currentDate.year; year++) {
      if (RamadanData.isRamadanYear(year)) {
        ramadanYears.add(year);
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ramadan Details (all days):',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        ...ramadanYears.map((year) => _buildRamadanYearItem(year)),
      ],
    );
  }

  Widget _buildRamadanYearItem(int year) {
    List<DateTime> ramadanDays = RamadanData.getRamadanDays(year);
    if (ramadanDays.isEmpty) return const SizedBox();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Ramadan $year',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${ramadanDays.length} days',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${ramadanDays.first.day}/${ramadanDays.first.month}/${ramadanDays.first.year} - ${ramadanDays.last.day}/${ramadanDays.last.month}/${ramadanDays.last.year}',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: ramadanDays.asMap().entries.map((entry) {
              int dayNumber = entry.key + 1;
              DateTime day = entry.value;
              bool isCompleted = DailyData.getFastingStatus(day, 'Ramadan');

              return GestureDetector(
                onTap: () {
                  setState(() {
                    DailyData.setFastingStatus(day, 'Ramadan', !isCompleted);
                  });
                },
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '$dayNumber',
                      style: TextStyle(
                        color: isCompleted
                            ? const Color(0xFF2E7D32)
                            : Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

// Other pages with glass design
class PrayerTimesPage extends StatelessWidget {
  const PrayerTimesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('Prayer Times'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: const Center(
        child: Text(
          'Prayer Times Page\n(Coming in Step 2)',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class QuranPage extends StatelessWidget {
  const QuranPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('Quran'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: const Center(
        child: Text(
          'Quran Page\n(Coming in Step 2)',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class TasbihPage extends StatelessWidget {
  const TasbihPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('Tasbih'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: const Center(
        child: Text(
          'Tasbih Page\n(Coming in Step 2)',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class CommunityPage extends StatelessWidget {
  const CommunityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('Community'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: const Center(
        child: Text(
          'Community Page\n(Coming in Step 2)',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}