import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:async';
import 'dart:convert';
// import 'package:http/http.dart' as http;
// import 'package:geolocator/geolocator.dart';
// import 'package:shared_preferences/shared_preferences.dart';

// Mock classes for web compatibility
class Position {
  final double latitude;
  final double longitude;

  Position({required this.latitude, required this.longitude});
}

enum LocationPermission {
  denied,
  deniedForever,
  granted,
}

class LocationAccuracy {
  static const high = 'high';
}

class LocationSettings {
  final String accuracy;
  const LocationSettings({required this.accuracy});
}

class Geolocator {
  static Future<bool> isLocationServiceEnabled() async => true;
  static Future<LocationPermission> checkPermission() async => LocationPermission.granted;
  static Future<LocationPermission> requestPermission() async => LocationPermission.granted;
  static Future<Position> getCurrentPosition({LocationSettings? locationSettings}) async {
    // Return London coordinates as default
    return Position(latitude: 51.5074, longitude: -0.1278);
  }
}

class http {
  static Future<HttpResponse> get(Uri uri) async {
    // For web version, return realistic London prayer times for today
    return HttpResponse(
      statusCode: 200,
      body: '''
      {
        "data": {
          "timings": {
            "Fajr": "02:56",
            "Dhuhr": "13:04",
            "Asr": "17:27",
            "Maghrib": "21:21",
            "Isha": "23:12"
          }
        }
      }
      '''
    );
  }
}

class HttpResponse {
  final int statusCode;
  final String body;

  HttpResponse({required this.statusCode, required this.body});
}

// Temporary SharedPreferences mock for web compatibility
class SharedPreferences {
  static final Map<String, dynamic> _storage = {};

  static Future<SharedPreferences> getInstance() async {
    return SharedPreferences();
  }

  String? getString(String key) {
    return _storage[key]?.toString();
  }

  int? getInt(String key) {
    final value = _storage[key];
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    return null;
  }

  bool? getBool(String key) {
    final value = _storage[key];
    if (value is bool) return value;
    if (value is String) return value.toLowerCase() == 'true';
    return null;
  }

  Future<bool> setString(String key, String value) async {
    _storage[key] = value;
    return true;
  }

  Future<bool> setInt(String key, int value) async {
    _storage[key] = value;
    return true;
  }

  Future<bool> setBool(String key, bool value) async {
    _storage[key] = value;
    return true;
  }

  Future<bool> remove(String key) async {
    _storage.remove(key);
    return true;
  }
}

// Prayer Times Service
class PrayerTimesService {
  static const String _baseUrl = 'http://api.aladhan.com/v1';

  // Get prayer times by coordinates
  static Future<Map<String, String>?> getPrayerTimesByCoordinates(
    double latitude,
    double longitude
  ) async {
    try {
      final now = DateTime.now();
      final dateStr = '${now.day.toString().padLeft(2, '0')}-${now.month.toString().padLeft(2, '0')}-${now.year}';

      final url = '$_baseUrl/timings/$dateStr?latitude=$latitude&longitude=$longitude&method=2';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final timings = data['data']['timings'];

        return {
          'Fajr': _formatTime(timings['Fajr']),
          'Dhuhr': _formatTime(timings['Dhuhr']),
          'Asr': _formatTime(timings['Asr']),
          'Maghrib': _formatTime(timings['Maghrib']),
          'Isha': _formatTime(timings['Isha']),
        };
      }
    } catch (e) {
      print('Error fetching prayer times: $e');
    }
    return null;
  }

  // Get prayer times by city name
  static Future<Map<String, String>?> getPrayerTimesByCity(String city) async {
    try {
      final now = DateTime.now();
      final dateStr = '${now.day.toString().padLeft(2, '0')}-${now.month.toString().padLeft(2, '0')}-${now.year}';

      final url = '$_baseUrl/timingsByCity/$dateStr?city=$city&method=2';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final timings = data['data']['timings'];

        return {
          'Fajr': _formatTime(timings['Fajr']),
          'Dhuhr': _formatTime(timings['Dhuhr']),
          'Asr': _formatTime(timings['Asr']),
          'Maghrib': _formatTime(timings['Maghrib']),
          'Isha': _formatTime(timings['Isha']),
        };
      }
    } catch (e) {
      print('Error fetching prayer times: $e');
    }
    return null;
  }

  static String _formatTime(String time) {
    // Remove timezone info and format to HH:MM
    return time.split(' ')[0];
  }
}

// Location Service
class LocationService {
  static Future<Position?> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print('Location services are disabled.');
        return null;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          print('Location permissions are denied');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        print('Location permissions are permanently denied');
        return null;
      }

      // Get current position
      return await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );
    } catch (e) {
      print('Error getting location: $e');
      return null;
    }
  }

  static Future<String?> getCityFromCoordinates(double latitude, double longitude) async {
    // Check if coordinates are London
    if ((latitude - 51.5074).abs() < 0.1 && (longitude - (-0.1278)).abs() < 0.1) {
      return 'London, UK';
    }
    // For other coordinates, return coordinates as string
    return '${latitude.toStringAsFixed(2)}, ${longitude.toStringAsFixed(2)}';
  }
}

// Notification settings management
class NotificationSettings {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Prayer reminder settings (minutes before prayer time)
  static int getPrayerReminderMinutes(String prayerName) {
    return _prefs?.getInt('reminder_${prayerName.toLowerCase()}') ?? 10;
  }

  static Future<void> setPrayerReminderMinutes(String prayerName, int minutes) async {
    await _prefs?.setInt('reminder_${prayerName.toLowerCase()}', minutes);
  }

  // Iqama reminder settings (enabled/disabled)
  static bool getIqamaReminderEnabled(String prayerName) {
    return _prefs?.getBool('iqama_reminder_${prayerName.toLowerCase()}') ?? true;
  }

  static Future<void> setIqamaReminderEnabled(String prayerName, bool enabled) async {
    await _prefs?.setBool('iqama_reminder_${prayerName.toLowerCase()}', enabled);
  }

  // Sound settings
  static String getNotificationSound() {
    return _prefs?.getString('notification_sound') ?? 'default';
  }

  static Future<void> setNotificationSound(String sound) async {
    await _prefs?.setString('notification_sound', sound);
  }

  // Vibration settings
  static bool getVibrationEnabled() {
    return _prefs?.getBool('vibration_enabled') ?? true;
  }

  static Future<void> setVibrationEnabled(bool enabled) async {
    await _prefs?.setBool('vibration_enabled', enabled);
  }

  // Master notification toggle
  static bool getNotificationsEnabled() {
    return _prefs?.getBool('notifications_enabled') ?? true;
  }

  static Future<void> setNotificationsEnabled(bool enabled) async {
    await _prefs?.setBool('notifications_enabled', enabled);
  }

  static Map<String, String> getAvailableSounds() {
    return {
      'default': 'Default Notification',
      'azan_makkah': 'Azan - Makkah',
      'azan_madinah': 'Azan - Madinah',
      'azan_egypt': 'Azan - Egypt',
      'bell': 'Simple Bell',
      'chime': 'Soft Chime',
      'silent': 'Silent (Vibration Only)',
    };
  }
}

// Notification service
class NotificationService {
  static final List<Timer> _activeTimers = [];

  static void scheduleAllNotifications(Map<String, String> prayerTimes) {
    // Clear existing timers
    clearAllNotifications();

    if (!NotificationSettings.getNotificationsEnabled()) {
      return;
    }

    DateTime now = DateTime.now();

    prayerTimes.forEach((prayerName, timeString) {
      _schedulePrayerNotifications(prayerName, timeString, now);
    });
  }

  static void _schedulePrayerNotifications(String prayerName, String timeString, DateTime now) {
    try {
      // Parse prayer time
      List<String> timeParts = timeString.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);

      DateTime prayerTime = DateTime(now.year, now.month, now.day, hour, minute);

      // If prayer time has passed today, schedule for tomorrow
      if (prayerTime.isBefore(now)) {
        prayerTime = prayerTime.add(const Duration(days: 1));
      }

      // Schedule prayer reminder
      int reminderMinutes = NotificationSettings.getPrayerReminderMinutes(prayerName);
      DateTime reminderTime = prayerTime.subtract(Duration(minutes: reminderMinutes));

      if (reminderTime.isAfter(now)) {
        Duration reminderDelay = reminderTime.difference(now);
        Timer reminderTimer = Timer(reminderDelay, () {
          _showPrayerReminder(prayerName, reminderMinutes);
        });
        _activeTimers.add(reminderTimer);
      }

      // Schedule iqama reminder
      if (NotificationSettings.getIqamaReminderEnabled(prayerName)) {
        int iqamaMinutes = IqamaSettings.getIqamaMinutes(prayerName);
        DateTime iqamaTime = prayerTime.add(Duration(minutes: iqamaMinutes));

        if (iqamaTime.isAfter(now)) {
          Duration iqamaDelay = iqamaTime.difference(now);
          Timer iqamaTimer = Timer(iqamaDelay, () {
            _showIqamaReminder(prayerName);
          });
          _activeTimers.add(iqamaTimer);
        }
      }
    } catch (e) {
      print('Error scheduling notification for $prayerName: $e');
    }
  }

  static void _showPrayerReminder(String prayerName, int minutesBefore) {
    String message = '$prayerName prayer in $minutesBefore minutes';
    _showNotification('Prayer Reminder', message);
  }

  static void _showIqamaReminder(String prayerName) {
    String message = '$prayerName Iqama time now!';
    _showNotification('Iqama Time', message);
  }

  static void _showNotification(String title, String message) {
    // For web, we'll use browser notifications
    // For mobile, this would use flutter_local_notifications

    print('🔔 NOTIFICATION: $title - $message');

    // Try to show browser notification (if permission granted)
    _showBrowserNotification(title, message);

    // Play sound
    _playNotificationSound();

    // Vibrate (if supported)
    if (NotificationSettings.getVibrationEnabled()) {
      _vibrate();
    }
  }

  static void _showBrowserNotification(String title, String message) {
    // This would work in a real web environment
    // For now, just log it
    print('🌐 Browser Notification: $title - $message');
  }

  static void _playNotificationSound() {
    String sound = NotificationSettings.getNotificationSound();
    print('🔊 Playing sound: $sound');
    // In real implementation, would play actual audio file
  }

  static void _vibrate() {
    print('📳 Vibrating...');
    // In real implementation, would trigger device vibration
  }

  static void clearAllNotifications() {
    for (Timer timer in _activeTimers) {
      timer.cancel();
    }
    _activeTimers.clear();
  }

  static void requestNotificationPermission() {
    // For web, request browser notification permission
    // For mobile, request local notification permission
    print('📱 Requesting notification permission...');
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize storage systems
  await DailyData.init();
  await UserProfileManager.init();
  await PrayerLocationSettings.init();
  await NotificationSettings.init();

  // Request notification permission
  NotificationService.requestNotificationPermission();

  runApp(const DadDnApp());
}

class DadDnApp extends StatelessWidget {
  const DadDnApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Dad Dn - Prayer & Fasting Tracker',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2E7D32),
        ),
      ),
      home: const HomePage(),
    );
  }
}

// Data models
class PrayerData {
  final String name;
  final String arabicName;
  final String time; // Prayer time
  bool isCompleted;

  PrayerData({
    required this.name,
    required this.arabicName,
    required this.time,
    this.isCompleted = false,
  });
}

class FastingData {
  final String type;
  final DateTime date;
  bool isCompleted;

  FastingData({
    required this.type,
    required this.date,
    this.isCompleted = false,
  });
}

// Ramadan dates data (real Islamic calendar dates)
class RamadanData {
  static final Map<int, Map<String, dynamic>> ramadanDates = {
    1990: {'start': DateTime(1990, 4, 17), 'end': DateTime(1990, 5, 16)},
    1991: {'start': DateTime(1991, 4, 6), 'end': DateTime(1991, 5, 5)},
    1992: {'start': DateTime(1992, 3, 26), 'end': DateTime(1992, 4, 24)},
    1993: {'start': DateTime(1993, 3, 15), 'end': DateTime(1993, 4, 13)},
    1994: {'start': DateTime(1994, 3, 4), 'end': DateTime(1994, 4, 2)},
    1995: {'start': DateTime(1995, 2, 21), 'end': DateTime(1995, 3, 22)},
    1996: {'start': DateTime(1996, 2, 10), 'end': DateTime(1996, 3, 10)},
    1997: {'start': DateTime(1997, 1, 30), 'end': DateTime(1997, 2, 28)},
    1998: {'start': DateTime(1998, 1, 19), 'end': DateTime(1998, 2, 17)},
    1999: {'start': DateTime(1999, 1, 8), 'end': DateTime(1999, 2, 6)},
    2000: {'start': DateTime(1999, 12, 28), 'end': DateTime(2000, 1, 26)},
    2001: {'start': DateTime(2001, 11, 17), 'end': DateTime(2001, 12, 16)},
    2002: {'start': DateTime(2002, 11, 6), 'end': DateTime(2002, 12, 5)},
    2003: {'start': DateTime(2003, 10, 26), 'end': DateTime(2003, 11, 24)},
    2004: {'start': DateTime(2004, 10, 15), 'end': DateTime(2004, 11, 13)},
    2005: {'start': DateTime(2005, 10, 4), 'end': DateTime(2005, 11, 2)},
    2006: {'start': DateTime(2006, 9, 23), 'end': DateTime(2006, 10, 22)},
    2007: {'start': DateTime(2007, 9, 13), 'end': DateTime(2007, 10, 12)},
    2008: {'start': DateTime(2008, 9, 1), 'end': DateTime(2008, 9, 30)},
    2009: {'start': DateTime(2009, 8, 21), 'end': DateTime(2009, 9, 19)},
    2010: {'start': DateTime(2010, 8, 11), 'end': DateTime(2010, 9, 9)},
    2011: {'start': DateTime(2011, 8, 1), 'end': DateTime(2011, 8, 30)},
    2012: {'start': DateTime(2012, 7, 20), 'end': DateTime(2012, 8, 18)},
    2013: {'start': DateTime(2013, 7, 9), 'end': DateTime(2013, 8, 7)},
    2014: {'start': DateTime(2014, 6, 28), 'end': DateTime(2014, 7, 27)},
    2015: {'start': DateTime(2015, 6, 18), 'end': DateTime(2015, 7, 17)},
    2016: {'start': DateTime(2016, 6, 6), 'end': DateTime(2016, 7, 5)},
    2017: {'start': DateTime(2017, 5, 27), 'end': DateTime(2017, 6, 25)},
    2018: {'start': DateTime(2018, 5, 16), 'end': DateTime(2018, 6, 14)},
    2019: {'start': DateTime(2019, 5, 5), 'end': DateTime(2019, 6, 3)},
    2020: {'start': DateTime(2020, 4, 24), 'end': DateTime(2020, 5, 23)},
    2021: {'start': DateTime(2021, 4, 13), 'end': DateTime(2021, 5, 12)},
    2022: {'start': DateTime(2022, 4, 2), 'end': DateTime(2022, 5, 1)},
    2023: {'start': DateTime(2023, 3, 23), 'end': DateTime(2023, 4, 21)},
    2024: {'start': DateTime(2024, 3, 11), 'end': DateTime(2024, 4, 9)},
    2025: {'start': DateTime(2025, 2, 28), 'end': DateTime(2025, 3, 29)},
    2026: {'start': DateTime(2026, 2, 17), 'end': DateTime(2026, 3, 18)},
    2027: {'start': DateTime(2027, 2, 6), 'end': DateTime(2027, 3, 7)},
    2028: {'start': DateTime(2028, 1, 26), 'end': DateTime(2028, 2, 24)},
    2029: {'start': DateTime(2029, 1, 15), 'end': DateTime(2029, 2, 13)},
    2030: {'start': DateTime(2030, 1, 5), 'end': DateTime(2030, 2, 3)},
  };

  static List<DateTime> getRamadanDays(int year) {
    final ramadan = ramadanDates[year];
    if (ramadan == null) return [];

    List<DateTime> days = [];
    DateTime current = ramadan['start'];
    DateTime end = ramadan['end'];

    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      days.add(current);
      current = current.add(const Duration(days: 1));
    }

    return days;
  }

  static bool isRamadanYear(int year) {
    return ramadanDates.containsKey(year);
  }
}

// Storage for prayer/fasting data by date with SharedPreferences
class DailyData {
  static final Map<String, Map<String, bool>> _prayerData = {};
  static final Map<String, Map<String, bool>> _fastingData = {};
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadData();
  }

  static Future<void> _loadData() async {
    if (_prefs == null) return;

    // Load prayer data
    final prayerJson = _prefs!.getString('prayer_data');
    if (prayerJson != null) {
      try {
        final Map<String, dynamic> decoded = json.decode(prayerJson);
        _prayerData.clear();
        decoded.forEach((key, value) {
          _prayerData[key] = Map<String, bool>.from(value);
        });
      } catch (e) {
        print('Error loading prayer data: $e');
      }
    }

    // Load fasting data
    final fastingJson = _prefs!.getString('fasting_data');
    if (fastingJson != null) {
      try {
        final Map<String, dynamic> decoded = json.decode(fastingJson);
        _fastingData.clear();
        decoded.forEach((key, value) {
          _fastingData[key] = Map<String, bool>.from(value);
        });
      } catch (e) {
        print('Error loading fasting data: $e');
      }
    }
  }

  static Future<void> _saveData() async {
    if (_prefs == null) return;

    try {
      // Save prayer data
      await _prefs!.setString('prayer_data', json.encode(_prayerData));

      // Save fasting data
      await _prefs!.setString('fasting_data', json.encode(_fastingData));
    } catch (e) {
      print('Error saving data: $e');
    }
  }

  static String _dateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  static bool getPrayerStatus(DateTime date, String prayerName) {
    final key = _dateKey(date);
    return _prayerData[key]?[prayerName] ?? false;
  }

  static Future<void> setPrayerStatus(DateTime date, String prayerName, bool status) async {
    final key = _dateKey(date);
    _prayerData[key] ??= {};
    _prayerData[key]![prayerName] = status;
    await _saveData();
  }

  static bool getFastingStatus(DateTime date, String fastingType) {
    final key = _dateKey(date);
    return _fastingData[key]?[fastingType] ?? false;
  }

  static Future<void> setFastingStatus(DateTime date, String fastingType, bool status) async {
    final key = _dateKey(date);
    _fastingData[key] ??= {};
    _fastingData[key]![fastingType] = status;
    await _saveData();
  }
}

// User profile management with SharedPreferences
class UserProfileManager {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  static Future<UserProfile?> loadProfile() async {
    if (_prefs == null) return null;

    final birthDateStr = _prefs!.getString('birth_date');
    final gender = _prefs!.getString('gender');

    if (birthDateStr != null && gender != null) {
      try {
        return UserProfile(
          birthDate: DateTime.parse(birthDateStr),
          gender: gender,
        );
      } catch (e) {
        print('Error loading profile: $e');
      }
    }

    return null;
  }

  static Future<void> saveProfile(UserProfile profile) async {
    if (_prefs == null) return;

    try {
      await _prefs!.setString('birth_date', profile.birthDate.toIso8601String());
      await _prefs!.setString('gender', profile.gender);
    } catch (e) {
      print('Error saving profile: $e');
    }
  }

  static Future<void> clearProfile() async {
    if (_prefs == null) return;

    try {
      await _prefs!.remove('birth_date');
      await _prefs!.remove('gender');
    } catch (e) {
      print('Error clearing profile: $e');
    }
  }
}

// Iqama settings management
class IqamaSettings {
  static final Map<String, int> _iqamaMinutes = {
    'Fajr': 10,
    'Dhuhr': 15,
    'Asr': 10,
    'Maghrib': 5,
    'Isha': 15,
  };

  static int getIqamaMinutes(String prayerName) {
    return _iqamaMinutes[prayerName] ?? 10;
  }

  static void setIqamaMinutes(String prayerName, int minutes) {
    _iqamaMinutes[prayerName] = minutes;
  }

  static Map<String, int> getAllSettings() {
    return Map.from(_iqamaMinutes);
  }
}

// Prayer location settings management
class PrayerLocationSettings {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  static String getCity() {
    return _prefs?.getString('selected_city') ?? 'London';
  }

  static String getCountry() {
    return _prefs?.getString('selected_country') ?? 'UK';
  }

  static int getMethod() {
    return _prefs?.getInt('calculation_method') ?? 2;
  }

  static Future<void> setCity(String city) async {
    await _prefs?.setString('selected_city', city);
  }

  static Future<void> setCountry(String country) async {
    await _prefs?.setString('selected_country', country);
  }

  static Future<void> setMethod(int method) async {
    await _prefs?.setInt('calculation_method', method);
  }

  static Map<int, String> getCalculationMethods() {
    return {
      1: 'University of Islamic Sciences, Karachi',
      2: 'Islamic Society of North America (ISNA)',
      3: 'Muslim World League',
      4: 'Umm Al-Qura University, Makkah',
      5: 'Egyptian General Authority of Survey',
      7: 'Institute of Geophysics, University of Tehran',
      8: 'Gulf Region',
      9: 'Kuwait',
      10: 'Qatar',
      11: 'Majlis Ugama Islam Singapura, Singapore',
      12: 'Union Organization islamic de France',
      13: 'Diyanet İşleri Başkanlığı, Turkey',
      14: 'Spiritual Administration of Muslims of Russia',
    };
  }
}

class UserProfile {
  final DateTime birthDate;
  final String gender;
  final DateTime obligationStartDate;

  UserProfile({
    required this.birthDate,
    required this.gender,
  }) : obligationStartDate = _calculateObligationStartDate(birthDate, gender);

  static DateTime _calculateObligationStartDate(DateTime birthDate, String gender) {
    int obligationAge = gender == 'male' ? 12 : 9;
    return DateTime(
      birthDate.year + obligationAge,
      birthDate.month,
      birthDate.day,
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  late List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [
      const PrayerTrackerPage(),
      const QuranPage(),
      const TasbihPage(),
      const DuaPage(),
      const CommunityPage(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF2E7D32),
              Color(0xFF4CAF50),
              Color(0xFF81C784),
            ],
          ),
        ),
        child: _pages[_selectedIndex],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _selectedIndex,
          onTap: (index) => setState(() => _selectedIndex = index),
          backgroundColor: Colors.transparent,
          selectedItemColor: const Color(0xFF2E7D32),
          unselectedItemColor: Colors.grey[600],
          elevation: 0,
          selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
          unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal),
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.mosque),
                  label: 'Prayer Tracker',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.menu_book),
                  label: 'Quran',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.radio_button_checked),
                  label: 'Tasbih',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.favorite),
                  label: 'Дуа',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.chat_bubble),
                  label: 'Чат',
                ),
              ],
            ),
        ),
    );
  }
}

// Prayer Tracker Page with full functionality
class PrayerTrackerPage extends StatefulWidget {
  const PrayerTrackerPage({super.key});

  @override
  State<PrayerTrackerPage> createState() => _PrayerTrackerPageState();
}

class _PrayerTrackerPageState extends State<PrayerTrackerPage> {
  DateTime selectedDate = DateTime.now();
  UserProfile? userProfile;
  DateTime currentTime = DateTime.now();
  StreamSubscription<DateTime>? timeSubscription;
  bool isRamadanExpanded = false;

  // Prayer times state - London times for today (01/07/2025)
  Map<String, String> prayerTimes = {
    'Fajr': '02:56',
    'Dhuhr': '13:04',
    'Asr': '17:27',
    'Maghrib': '21:21',
    'Isha': '23:12',
  };
  String? currentLocation;
  bool isLoadingLocation = false;

  // Location settings
  String selectedCity = 'London';
  String selectedCountry = 'UK';
  int selectedMethod = 2; // ISNA method

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
    _loadLocationSettings();
    _loadPrayerTimes();

    // Real-time clock stream
    timeSubscription = Stream.periodic(const Duration(seconds: 1), (_) => DateTime.now())
        .listen((time) {
      if (mounted) {
        setState(() {
          currentTime = time;
        });
      }
    });
  }

  Future<void> _loadUserProfile() async {
    final profile = await UserProfileManager.loadProfile();
    if (mounted) {
      setState(() {
        userProfile = profile;
      });
    }
  }

  void _loadLocationSettings() {
    selectedCity = PrayerLocationSettings.getCity();
    selectedCountry = PrayerLocationSettings.getCountry();
    selectedMethod = PrayerLocationSettings.getMethod();
  }

  Future<void> _saveLocationSettings(String city, String country, int method) async {
    // Save to storage
    await PrayerLocationSettings.setCity(city);
    await PrayerLocationSettings.setCountry(country);
    await PrayerLocationSettings.setMethod(method);

    // Update local state
    setState(() {
      selectedCity = city;
      selectedCountry = country;
      selectedMethod = method;
    });

    // Reload prayer times with new settings
    _loadPrayerTimes();
  }

  Future<void> _saveNotificationSettings(
    bool notificationsEnabled,
    bool vibrationEnabled,
    String sound,
    Map<String, int> reminderMinutes,
    Map<String, bool> iqamaEnabled,
  ) async {
    // Save all settings
    await NotificationSettings.setNotificationsEnabled(notificationsEnabled);
    await NotificationSettings.setVibrationEnabled(vibrationEnabled);
    await NotificationSettings.setNotificationSound(sound);

    // Save prayer reminder minutes
    for (var entry in reminderMinutes.entries) {
      await NotificationSettings.setPrayerReminderMinutes(entry.key, entry.value);
    }

    // Save iqama reminder settings
    for (var entry in iqamaEnabled.entries) {
      await NotificationSettings.setIqamaReminderEnabled(entry.key, entry.value);
    }

    // Reschedule notifications with new settings
    NotificationService.scheduleAllNotifications(prayerTimes);
  }

  Future<void> _loadPrayerTimes() async {
    setState(() {
      isLoadingLocation = true;
    });

    try {
      // Try to get current location
      Position? position = await LocationService.getCurrentLocation();

      if (position != null) {
        // Get prayer times by city (using selected settings)
        Map<String, String>? times = await PrayerTimesService.getPrayerTimesByCity(
          selectedCity
        );

        if (mounted && times != null) {
          setState(() {
            prayerTimes = times;
            currentLocation = '$selectedCity, $selectedCountry';
            isLoadingLocation = false;
          });

          // Schedule notifications for new prayer times
          NotificationService.scheduleAllNotifications(times);
          return;
        }
      }
    } catch (e) {
      print('Error loading prayer times: $e');
    }

    // Fallback to default times
    if (mounted) {
      setState(() {
        currentLocation = '$selectedCity, $selectedCountry';
        isLoadingLocation = false;
      });

      // Schedule notifications for default prayer times
      NotificationService.scheduleAllNotifications(prayerTimes);
    }
  }

  @override
  void dispose() {
    // Cancel any active streams/timers
    timeSubscription?.cancel();
    NotificationService.clearAllNotifications();
    super.dispose();
  }

  List<PrayerData> get prayers {
    return [
      PrayerData(name: 'Fajr', arabicName: 'الفجر', time: prayerTimes['Fajr'] ?? '05:30'),
      PrayerData(name: 'Dhuhr', arabicName: 'الظهر', time: prayerTimes['Dhuhr'] ?? '12:15'),
      PrayerData(name: 'Asr', arabicName: 'العصر', time: prayerTimes['Asr'] ?? '15:45'),
      PrayerData(name: 'Maghrib', arabicName: 'المغرب', time: prayerTimes['Maghrib'] ?? '18:20'),
      PrayerData(name: 'Isha', arabicName: 'العشاء', time: prayerTimes['Isha'] ?? '19:50'),
    ].map((prayer) {
      prayer.isCompleted = DailyData.getPrayerStatus(selectedDate, prayer.name);
      return prayer;
    }).toList();
  }

  // Removed fasting getter - now handled by Ramadan details

  int get totalMissedPrayers {
    if (userProfile == null) return 0;

    DateTime startDate = userProfile!.obligationStartDate;
    DateTime endDate = DateTime.now();

    if (endDate.isBefore(startDate)) return 0;

    int totalPrayers = 0;
    int completedPrayers = 0;

    // Count all prayers from obligation start to now
    DateTime current = startDate;
    while (current.isBefore(endDate) || current.isAtSameMomentAs(endDate)) {
      totalPrayers += 5; // 5 prayers per day

      // Count completed prayers for this day
      for (String prayerName in ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha']) {
        if (DailyData.getPrayerStatus(current, prayerName)) {
          completedPrayers++;
        }
      }

      current = current.add(const Duration(days: 1));
    }

    return totalPrayers - completedPrayers;
  }

  int get totalMissedFasting {
    if (userProfile == null) return 0;

    DateTime startDate = userProfile!.obligationStartDate;
    DateTime currentDate = DateTime.now();

    if (currentDate.isBefore(startDate)) return 0;

    int totalFastingDays = 0;
    int completedFastingDays = 0;

    // Count all Ramadan days from obligation start to now
    for (int year = startDate.year; year <= currentDate.year; year++) {
      if (RamadanData.isRamadanYear(year)) {
        List<DateTime> ramadanDays = RamadanData.getRamadanDays(year);
        for (DateTime day in ramadanDays) {
          // Include days that are after obligation start and before/equal to current date
          if ((day.isAfter(startDate) || day.isAtSameMomentAs(startDate)) &&
              (day.isBefore(currentDate) || day.isAtSameMomentAs(currentDate))) {
            totalFastingDays++;
            if (DailyData.getFastingStatus(day, 'Ramadan')) {
              completedFastingDays++;
            }
          }
        }
      }
    }

    return totalFastingDays - completedFastingDays;
  }

  String get nextPrayerCountdown {
    List<Map<String, dynamic>> prayerTimesList = [
      {'name': 'Fajr', 'time': _parseTimeString(prayerTimes['Fajr'] ?? '05:30')},
      {'name': 'Dhuhr', 'time': _parseTimeString(prayerTimes['Dhuhr'] ?? '12:15')},
      {'name': 'Asr', 'time': _parseTimeString(prayerTimes['Asr'] ?? '15:45')},
      {'name': 'Maghrib', 'time': _parseTimeString(prayerTimes['Maghrib'] ?? '18:20')},
      {'name': 'Isha', 'time': _parseTimeString(prayerTimes['Isha'] ?? '19:50')},
    ];

    TimeOfDay now = TimeOfDay.fromDateTime(currentTime);
    int nowMinutes = now.hour * 60 + now.minute;

    for (var prayer in prayerTimesList) {
      String prayerName = prayer['name'];
      TimeOfDay prayerTime = prayer['time'];
      int iqamaMinutes = IqamaSettings.getIqamaMinutes(prayerName);

      // Calculate Iqama time with proper overflow handling
      int totalMinutes = prayerTime.hour * 60 + prayerTime.minute + iqamaMinutes;
      int iqamaHour = (totalMinutes ~/ 60) % 24;
      int iqamaMinute = totalMinutes % 60;
      int iqamaTotalMinutes = iqamaHour * 60 + iqamaMinute;

      if (iqamaTotalMinutes > nowMinutes) {
        int diffMinutes = iqamaTotalMinutes - nowMinutes;
        int hours = diffMinutes ~/ 60;
        int minutes = diffMinutes % 60;
        return '$prayerName Iqama in ${hours}h ${minutes}m';
      }
    }

    // If no prayer today, next is Fajr tomorrow
    TimeOfDay fajr = prayerTimesList[0]['time'];
    int fajrIqama = IqamaSettings.getIqamaMinutes('Fajr');
    int fajrTotalMinutes = fajr.hour * 60 + fajr.minute + fajrIqama;
    int fajrIqamaHour = (fajrTotalMinutes ~/ 60) % 24;
    int fajrIqamaMinute = fajrTotalMinutes % 60;
    int fajrIqamaTotalMinutes = fajrIqamaHour * 60 + fajrIqamaMinute;

    // Calculate time until tomorrow's Fajr
    int minutesUntilMidnight = (24 * 60) - nowMinutes;
    int totalMinutesUntilFajr = minutesUntilMidnight + fajrIqamaTotalMinutes;
    int hours = totalMinutesUntilFajr ~/ 60;
    int minutes = totalMinutesUntilFajr % 60;

    return 'Fajr Iqama in ${hours}h ${minutes}m';
  }



  Duration _timeDifference(TimeOfDay from, TimeOfDay to) {
    int fromMinutes = from.hour * 60 + from.minute;
    int toMinutes = to.hour * 60 + to.minute;
    return Duration(minutes: toMinutes - fromMinutes);
  }

  TimeOfDay _parseTimeString(String timeString) {
    try {
      List<String> parts = timeString.split(':');
      int hour = int.parse(parts[0]);
      int minute = int.parse(parts[1]);
      return TimeOfDay(hour: hour, minute: minute);
    } catch (e) {
      // Fallback to default time
      return const TimeOfDay(hour: 12, minute: 0);
    }
  }

  String _getPrayerTimeOrCountdown(String prayerName) {
    Map<String, TimeOfDay> prayerTimesMap = {
      'Fajr': _parseTimeString(prayerTimes['Fajr'] ?? '05:30'),
      'Dhuhr': _parseTimeString(prayerTimes['Dhuhr'] ?? '12:15'),
      'Asr': _parseTimeString(prayerTimes['Asr'] ?? '15:45'),
      'Maghrib': _parseTimeString(prayerTimes['Maghrib'] ?? '18:20'),
      'Isha': _parseTimeString(prayerTimes['Isha'] ?? '19:50'),
    };

    TimeOfDay prayerTime = prayerTimesMap[prayerName]!;
    TimeOfDay now = TimeOfDay.fromDateTime(currentTime);
    int iqamaMinutes = IqamaSettings.getIqamaMinutes(prayerName);

    // Calculate Iqama time
    int totalMinutes = prayerTime.hour * 60 + prayerTime.minute + iqamaMinutes;
    int iqamaHour = (totalMinutes ~/ 60) % 24;
    int iqamaMinute = totalMinutes % 60;

    TimeOfDay iqamaTime = TimeOfDay(hour: iqamaHour, minute: iqamaMinute);

    String prayerTimeStr = '${prayerTime.hour.toString().padLeft(2, '0')}:${prayerTime.minute.toString().padLeft(2, '0')}';
    String iqamaTimeStr = '${iqamaTime.hour.toString().padLeft(2, '0')}:${iqamaTime.minute.toString().padLeft(2, '0')}';

    // Check if iqama time has passed
    int nowMinutes = now.hour * 60 + now.minute;
    int iqamaTotalMinutes = iqamaTime.hour * 60 + iqamaTime.minute;

    if (iqamaTotalMinutes > nowMinutes) {
      // Iqama is in the future - show countdown
      Duration diff = _timeDifference(now, iqamaTime);
      return '$prayerTimeStr | Iqama in ${diff.inHours}h ${diff.inMinutes % 60}m';
    } else {
      // Iqama has passed - just show times
      return '$prayerTimeStr | Iqama $iqamaTimeStr';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      drawer: _buildDrawer(),
      appBar: AppBar(
        title: const Text('Prayer Tracker'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(isLoadingLocation ? Icons.refresh : Icons.location_on),
            onPressed: isLoadingLocation ? null : () => _showLocationSettingsDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () => _showNotificationSettingsDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.schedule),
            onPressed: () => _showIqamaSettingsDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () => _showProfileDialog(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(MediaQuery.of(context).size.width < 600 ? 12.0 : 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Real-time clock and statistics
              _buildGlassCard(
                child: Column(
                  children: [
                    Text(
                      '${currentTime.hour.toString().padLeft(2, '0')}:${currentTime.minute.toString().padLeft(2, '0')}:${currentTime.second.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width < 600 ? 28 : 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      nextPrayerCountdown,
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width < 600 ? 14 : 16,
                        color: Colors.white70,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (currentLocation != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            isLoadingLocation ? Icons.refresh : Icons.location_on,
                            color: Colors.white70,
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            isLoadingLocation ? 'Loading location...' : currentLocation!,
                            style: TextStyle(
                              fontSize: MediaQuery.of(context).size.width < 600 ? 11 : 12,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ],
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem('Missed Prayers', totalMissedPrayers.toString()),
                        _buildStatItem('Missed Fasting', totalMissedFasting.toString()),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Date Navigation
              _buildGlassCard(
                child: Column(
                  children: [
                    const Text(
                      'Select Date',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              selectedDate = selectedDate.subtract(const Duration(days: 1));
                            });
                          },
                          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                        ),
                        GestureDetector(
                          onTap: () => _selectDate(),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: selectedDate.isBefore(DateTime.now()) ? () {
                            setState(() {
                              selectedDate = selectedDate.add(const Duration(days: 1));
                            });
                          } : null,
                          icon: const Icon(Icons.arrow_forward_ios, color: Colors.white),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              _buildGlassCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Missed Prayers',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...prayers.map((prayer) => _buildPrayerCheckbox(prayer)),
                  ],
                ),
              ),

            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGlassCard({required Widget child}) {
    return Container(
      padding: EdgeInsets.all(MediaQuery.of(context).size.width < 600 ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: child,
        ),
      ),
    );
  }

  Widget _buildPrayerCheckbox(PrayerData prayer) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          GestureDetector(
            onTap: () async {
              setState(() {
                prayer.isCompleted = !prayer.isCompleted;
              });
              await DailyData.setPrayerStatus(selectedDate, prayer.name, prayer.isCompleted);
            },
            child: Container(
              width: MediaQuery.of(context).size.width < 600 ? 32 : 28,
              height: MediaQuery.of(context).size.width < 600 ? 32 : 28,
              decoration: BoxDecoration(
                color: prayer.isCompleted
                    ? Colors.white
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.white,
                  width: 2,
                ),
                boxShadow: prayer.isCompleted ? [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ] : null,
              ),
              child: prayer.isCompleted
                  ? Icon(
                      Icons.check,
                      color: const Color(0xFF2E7D32),
                      size: MediaQuery.of(context).size.width < 600 ? 22 : 20,
                    )
                  : null,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      prayer.name,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: MediaQuery.of(context).size.width < 600 ? 18 : 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _getPrayerTimeOrCountdown(prayer.name),
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: MediaQuery.of(context).size.width < 600 ? 13 : 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  prayer.arabicName,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildDrawer() {
    return Drawer(
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF2E7D32),
              Color(0xFF4CAF50),
              Color(0xFF81C784),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                child: const Text(
                  'Menu',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                child: ListView(
                  children: [
                    _buildDrawerItem(
                      icon: Icons.calendar_month,
                      title: 'Ramadan Fasting',
                      onTap: () {
                        Navigator.pop(context);
                        _showRamadanPage();
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.white),
      title: Text(
        title,
        style: const TextStyle(color: Colors.white),
      ),
      onTap: onTap,
    );
  }

  void _showRamadanPage() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RamadanPage(userProfile: userProfile),
      ),
    );
  }

  void _showLocationSettingsDialog() {
    String tempCity = selectedCity;
    String tempCountry = selectedCountry;
    int tempMethod = selectedMethod;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Location & Prayer Settings'),
          content: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // City input
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'City',
                    border: OutlineInputBorder(),
                  ),
                  controller: TextEditingController(text: tempCity),
                  onChanged: (value) {
                    tempCity = value;
                  },
                ),
                const SizedBox(height: 16),

                // Country input
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Country',
                    border: OutlineInputBorder(),
                  ),
                  controller: TextEditingController(text: tempCountry),
                  onChanged: (value) {
                    tempCountry = value;
                  },
                ),
                const SizedBox(height: 16),

                // Calculation method dropdown
                DropdownButtonFormField<int>(
                  decoration: const InputDecoration(
                    labelText: 'Calculation Method',
                    border: OutlineInputBorder(),
                  ),
                  value: tempMethod,
                  items: PrayerLocationSettings.getCalculationMethods().entries.map((entry) {
                    return DropdownMenuItem<int>(
                      value: entry.key,
                      child: Text(
                        entry.value,
                        style: const TextStyle(fontSize: 12),
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setDialogState(() {
                        tempMethod = value;
                      });
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                // Save settings and update state
                _saveLocationSettings(tempCity, tempCountry, tempMethod);
                Navigator.of(context).pop();
              },
              child: const Text('Save & Reload'),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotificationSettingsDialog() {
    bool tempNotificationsEnabled = NotificationSettings.getNotificationsEnabled();
    bool tempVibrationEnabled = NotificationSettings.getVibrationEnabled();
    String tempSound = NotificationSettings.getNotificationSound();

    Map<String, int> tempReminderMinutes = {
      'Fajr': NotificationSettings.getPrayerReminderMinutes('Fajr'),
      'Dhuhr': NotificationSettings.getPrayerReminderMinutes('Dhuhr'),
      'Asr': NotificationSettings.getPrayerReminderMinutes('Asr'),
      'Maghrib': NotificationSettings.getPrayerReminderMinutes('Maghrib'),
      'Isha': NotificationSettings.getPrayerReminderMinutes('Isha'),
    };

    Map<String, bool> tempIqamaEnabled = {
      'Fajr': NotificationSettings.getIqamaReminderEnabled('Fajr'),
      'Dhuhr': NotificationSettings.getIqamaReminderEnabled('Dhuhr'),
      'Asr': NotificationSettings.getIqamaReminderEnabled('Asr'),
      'Maghrib': NotificationSettings.getIqamaReminderEnabled('Maghrib'),
      'Isha': NotificationSettings.getIqamaReminderEnabled('Isha'),
    };

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Notification Settings'),
          content: SizedBox(
            width: 400,
            height: 500,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Master toggle
                  SwitchListTile(
                    title: const Text('Enable Notifications'),
                    subtitle: const Text('Master switch for all notifications'),
                    value: tempNotificationsEnabled,
                    onChanged: (value) {
                      setDialogState(() {
                        tempNotificationsEnabled = value;
                      });
                    },
                  ),

                  const Divider(),

                  // Sound settings
                  const Text('Notification Sound:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    value: tempSound,
                    items: NotificationSettings.getAvailableSounds().entries.map((entry) {
                      return DropdownMenuItem<String>(
                        value: entry.key,
                        child: Text(entry.value),
                      );
                    }).toList(),
                    onChanged: tempNotificationsEnabled ? (value) {
                      if (value != null) {
                        setDialogState(() {
                          tempSound = value;
                        });
                      }
                    } : null,
                  ),

                  const SizedBox(height: 16),

                  // Vibration toggle
                  SwitchListTile(
                    title: const Text('Vibration'),
                    subtitle: const Text('Vibrate on notifications'),
                    value: tempVibrationEnabled,
                    onChanged: tempNotificationsEnabled ? (value) {
                      setDialogState(() {
                        tempVibrationEnabled = value;
                      });
                    } : null,
                  ),

                  const Divider(),

                  // Prayer reminders
                  const Text('Prayer Reminders:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  ...tempReminderMinutes.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(entry.key),
                          Row(
                            children: [
                              IconButton(
                                onPressed: tempNotificationsEnabled && entry.value > 0 ? () {
                                  setDialogState(() {
                                    tempReminderMinutes[entry.key] = entry.value - 1;
                                  });
                                } : null,
                                icon: const Icon(Icons.remove),
                              ),
                              Container(
                                width: 60,
                                padding: const EdgeInsets.symmetric(vertical: 4),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  '${entry.value} min',
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ),
                              IconButton(
                                onPressed: tempNotificationsEnabled && entry.value < 30 ? () {
                                  setDialogState(() {
                                    tempReminderMinutes[entry.key] = entry.value + 1;
                                  });
                                } : null,
                                icon: const Icon(Icons.add),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  }),

                  const Divider(),

                  // Iqama reminders
                  const Text('Iqama Reminders:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  ...tempIqamaEnabled.entries.map((entry) {
                    return SwitchListTile(
                      title: Text('${entry.key} Iqama'),
                      value: entry.value,
                      onChanged: tempNotificationsEnabled ? (value) {
                        setDialogState(() {
                          tempIqamaEnabled[entry.key] = value;
                        });
                      } : null,
                    );
                  }),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                _saveNotificationSettings(
                  tempNotificationsEnabled,
                  tempVibrationEnabled,
                  tempSound,
                  tempReminderMinutes,
                  tempIqamaEnabled,
                );
                Navigator.of(context).pop();
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    int numValue = int.tryParse(value) ?? 0;

    return Column(
      children: [
        if (numValue == 0) ...[
          // Show beautiful checkmark when no missed prayers/fasting
          Container(
            width: MediaQuery.of(context).size.width < 600 ? 36 : 40,
            height: MediaQuery.of(context).size.width < 600 ? 36 : 40,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.3),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Icon(
              Icons.check,
              color: const Color(0xFF2E7D32),
              size: MediaQuery.of(context).size.width < 600 ? 20 : 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Complete!',
            style: TextStyle(
              color: Colors.white,
              fontSize: MediaQuery.of(context).size.width < 600 ? 11 : 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ] else ...[
          // Show number when there are missed prayers/fasting
          Text(
            value,
            style: TextStyle(
              color: Colors.white,
              fontSize: MediaQuery.of(context).size.width < 600 ? 20 : 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: userProfile?.obligationStartDate ?? DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  void _showProfileDialog() {
    if (userProfile == null) {
      _showProfileSetupDialog();
    } else {
      _showProfileViewDialog();
    }
  }

  void _showProfileSetupDialog() {
    DateTime selectedBirthDate = DateTime(2000, 1, 1);
    String selectedGender = 'male';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Setup Profile'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Birth Date:'),
              const SizedBox(height: 8),
              GestureDetector(
                onTap: () async {
                  final DateTime? picked = await showDatePicker(
                    context: context,
                    initialDate: selectedBirthDate,
                    firstDate: DateTime(1950),
                    lastDate: DateTime.now(),
                  );
                  if (picked != null) {
                    setDialogState(() {
                      selectedBirthDate = picked;
                    });
                  }
                },
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${selectedBirthDate.day}/${selectedBirthDate.month}/${selectedBirthDate.year}',
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text('Gender:'),
              const SizedBox(height: 8),
              Row(
                children: [
                  Radio<String>(
                    value: 'male',
                    groupValue: selectedGender,
                    onChanged: (value) {
                      setDialogState(() {
                        selectedGender = value!;
                      });
                    },
                  ),
                  const Text('Male'),
                  Radio<String>(
                    value: 'female',
                    groupValue: selectedGender,
                    onChanged: (value) {
                      setDialogState(() {
                        selectedGender = value!;
                      });
                    },
                  ),
                  const Text('Female'),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final profile = UserProfile(
                  birthDate: selectedBirthDate,
                  gender: selectedGender,
                );
                await UserProfileManager.saveProfile(profile);
                if (mounted) {
                  setState(() {
                    userProfile = profile;
                  });
                }
                navigator.pop();
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _showProfileViewDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('User Profile'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Birth Date: ${userProfile?.birthDate.day}/${userProfile?.birthDate.month}/${userProfile?.birthDate.year}'),
            Text('Gender: ${userProfile?.gender}'),
            Text('Obligation Start: ${userProfile?.obligationStartDate.day}/${userProfile?.obligationStartDate.month}/${userProfile?.obligationStartDate.year}'),
            const SizedBox(height: 16),
            Text('Total Missed Prayers: $totalMissedPrayers'),
            Text('Total Missed Fasting: $totalMissedFasting'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showProfileSetupDialog();
            },
            child: const Text('Edit'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showIqamaSettingsDialog() {
    Map<String, int> settings = IqamaSettings.getAllSettings();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Iqama Settings'),
          content: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: settings.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        entry.key,
                        style: const TextStyle(fontSize: 16),
                      ),
                      Row(
                        children: [
                          IconButton(
                            onPressed: () {
                              if (entry.value > 0) {
                                setDialogState(() {
                                  settings[entry.key] = entry.value - 1;
                                });
                              }
                            },
                            icon: const Icon(Icons.remove),
                          ),
                          Container(
                            width: 60,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '${entry.value} min',
                              textAlign: TextAlign.center,
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              if (entry.value < 60) {
                                setDialogState(() {
                                  settings[entry.key] = entry.value + 1;
                                });
                              }
                            },
                            icon: const Icon(Icons.add),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                // Save settings
                settings.forEach((key, value) {
                  IqamaSettings.setIqamaMinutes(key, value);
                });
                setState(() {
                  // Refresh UI
                });
                Navigator.of(context).pop();
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }




}

// Separate widget for better performance
class RamadanYearWidget extends StatefulWidget {
  final int year;
  final VoidCallback onDayToggled;

  const RamadanYearWidget({
    super.key,
    required this.year,
    required this.onDayToggled,
  });

  @override
  State<RamadanYearWidget> createState() => _RamadanYearWidgetState();
}

class _RamadanYearWidgetState extends State<RamadanYearWidget> {
  List<DateTime> ramadanDays = [];
  Map<DateTime, bool> dayStatus = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    ramadanDays = RamadanData.getRamadanDays(widget.year);
    for (DateTime day in ramadanDays) {
      dayStatus[day] = DailyData.getFastingStatus(day, 'Ramadan');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (ramadanDays.isEmpty) return const SizedBox();

    // Calculate completed days for this year
    int completedDays = dayStatus.values.where((status) => status).length;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Ramadan ${widget.year}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '$completedDays/${ramadanDays.length} days',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${ramadanDays.first.day}/${ramadanDays.first.month}/${ramadanDays.first.year} - ${ramadanDays.last.day}/${ramadanDays.last.month}/${ramadanDays.last.year}',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: ramadanDays.asMap().entries.map((entry) {
              int dayNumber = entry.key + 1;
              DateTime day = entry.value;
              bool isCompleted = dayStatus[day] ?? false;

              return GestureDetector(
                onTap: () async {
                  // Update local state immediately
                  setState(() {
                    dayStatus[day] = !isCompleted;
                  });

                  // Save to storage
                  await DailyData.setFastingStatus(day, 'Ramadan', dayStatus[day]!);

                  // Notify parent to update counters
                  widget.onDayToggled();
                },
                child: Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '$dayNumber',
                      style: TextStyle(
                        color: isCompleted
                            ? const Color(0xFF2E7D32)
                            : Colors.white,
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

// Other pages with glass design

class QuranPage extends StatelessWidget {
  const QuranPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('Quran'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: const Center(
        child: Text(
          'Quran Page\n(Coming in Step 2)',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class TasbihPage extends StatefulWidget {
  const TasbihPage({super.key});

  @override
  State<TasbihPage> createState() => _TasbihPageState();
}

class _TasbihPageState extends State<TasbihPage> {
  int currentCount = 0;
  int targetCount = 33;
  String currentDhikr = 'سُبْحَانَ اللهِ';
  String currentDhikrTranslation = 'SubhanAllah';

  // Список зикров
  final List<Map<String, dynamic>> dhikrList = [
    {
      'arabic': 'سُبْحَانَ اللهِ',
      'transliteration': 'SubhanAllah',
      'translation': 'Glory be to Allah',
      'count': 33,
    },
    {
      'arabic': 'الْحَمْدُ لِلَّهِ',
      'transliteration': 'Alhamdulillah',
      'translation': 'Praise be to Allah',
      'count': 33,
    },
    {
      'arabic': 'اللهُ أَكْبَرُ',
      'transliteration': 'Allahu Akbar',
      'translation': 'Allah is the Greatest',
      'count': 34,
    },
    {
      'arabic': 'لَا إِلَهَ إِلَّا اللهُ',
      'transliteration': 'La ilaha illa Allah',
      'translation': 'There is no god but Allah',
      'count': 100,
    },
    {
      'arabic': 'أَسْتَغْفِرُ اللهَ',
      'transliteration': 'Astaghfirullah',
      'translation': 'I seek forgiveness from Allah',
      'count': 100,
    },
    {
      'arabic': 'سُبْحَانَ اللهِ وَبِحَمْدِهِ',
      'transliteration': 'SubhanAllahi wa bihamdihi',
      'translation': 'Glory be to Allah and praise Him',
      'count': 100,
    },
  ];

  int selectedDhikrIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadSelectedDhikr();
  }

  void _loadSelectedDhikr() {
    final dhikr = dhikrList[selectedDhikrIndex];
    setState(() {
      currentDhikr = dhikr['arabic'];
      currentDhikrTranslation = dhikr['transliteration'];
      targetCount = dhikr['count'];
    });
  }

  void _incrementCount() {
    setState(() {
      currentCount++;
      if (currentCount >= targetCount) {
        _showCompletionDialog();
      }
    });
  }

  void _resetCount() {
    setState(() {
      currentCount = 0;
    });
  }

  void _showCompletionDialog() {
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 600;
    final scaleFactor = isDesktop ? 1.0 : screenSize.width / 400;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          '🎉 Завершено!',
          style: TextStyle(fontSize: 20 * scaleFactor),
        ),
        content: Text(
          'Вы завершили $targetCount повторений\n$currentDhikrTranslation',
          style: TextStyle(fontSize: 16 * scaleFactor),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetCount();
            },
            child: Text(
              'Начать заново',
              style: TextStyle(fontSize: 14 * scaleFactor),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _nextDhikr();
            },
            child: Text(
              'Следующий зикр',
              style: TextStyle(fontSize: 14 * scaleFactor),
            ),
          ),
        ],
      ),
    );
  }

  void _nextDhikr() {
    setState(() {
      selectedDhikrIndex = (selectedDhikrIndex + 1) % dhikrList.length;
      currentCount = 0;
    });
    _loadSelectedDhikr();
  }

  void _showDhikrSelector() {
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 600;
    final scaleFactor = isDesktop ? 1.0 : screenSize.width / 400;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Выберите зикр',
          style: TextStyle(fontSize: 18 * scaleFactor),
        ),
        content: SizedBox(
          width: 300 * scaleFactor,
          height: 400 * scaleFactor,
          child: ListView.builder(
            itemCount: dhikrList.length,
            itemBuilder: (context, index) {
              final dhikr = dhikrList[index];
              return ListTile(
                selected: index == selectedDhikrIndex,
                title: Text(
                  dhikr['arabic'],
                  style: TextStyle(fontSize: 18 * scaleFactor),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      dhikr['transliteration'],
                      style: TextStyle(fontSize: 14 * scaleFactor),
                    ),
                    Text(
                      dhikr['translation'],
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12 * scaleFactor,
                      ),
                    ),
                    Text(
                      'Цель: ${dhikr['count']} раз',
                      style: TextStyle(
                        color: Colors.blue[600],
                        fontSize: 12 * scaleFactor,
                      ),
                    ),
                  ],
                ),
                onTap: () {
                  setState(() {
                    selectedDhikrIndex = index;
                    currentCount = 0;
                  });
                  _loadSelectedDhikr();
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Закрыть',
              style: TextStyle(fontSize: 14 * scaleFactor),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double progress = targetCount > 0 ? currentCount / targetCount : 0;

    // Адаптивные размеры для разных экранов
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 600;
    final scaleFactor = isDesktop ? 1.0 : screenSize.width / 400;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1E3A8A),
              Color(0xFF3B82F6),
              Color(0xFF60A5FA),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(20 * scaleFactor),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '📿 Тасбих',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24 * scaleFactor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed: _showDhikrSelector,
                      icon: Icon(Icons.list,
                        color: Colors.white,
                        size: 24 * scaleFactor),
                    ),
                  ],
                ),
              ),

              // Main content
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(20 * scaleFactor),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Progress indicator
                      Container(
                        width: 200 * scaleFactor,
                        height: 200 * scaleFactor,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withOpacity(0.1),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 2 * scaleFactor,
                          ),
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // Progress circle
                            SizedBox(
                              width: 180 * scaleFactor,
                              height: 180 * scaleFactor,
                              child: CircularProgressIndicator(
                                value: progress,
                                strokeWidth: 8 * scaleFactor,
                                backgroundColor: Colors.white.withOpacity(0.2),
                                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            // Count display
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '$currentCount',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 48 * scaleFactor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  '/ $targetCount',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.8),
                                    fontSize: 18 * scaleFactor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 40 * scaleFactor),

                      // Current dhikr display
                      Container(
                        padding: EdgeInsets.all(20 * scaleFactor),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(16 * scaleFactor),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              currentDhikr,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 32 * scaleFactor,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 8 * scaleFactor),
                            Text(
                              currentDhikrTranslation,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.9),
                                fontSize: 18 * scaleFactor,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 40 * scaleFactor),

                      // Action buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // Reset button
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withOpacity(0.2),
                            ),
                            child: IconButton(
                              onPressed: _resetCount,
                              icon: Icon(
                                Icons.refresh,
                                color: Colors.white,
                                size: 30 * scaleFactor,
                              ),
                            ),
                          ),

                          // Main counter button
                          GestureDetector(
                            onTap: _incrementCount,
                            child: Container(
                              width: 120 * scaleFactor,
                              height: 120 * scaleFactor,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 10 * scaleFactor,
                                    offset: Offset(0, 5 * scaleFactor),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.add,
                                color: const Color(0xFF1E3A8A),
                                size: 50 * scaleFactor,
                              ),
                            ),
                          ),

                          // Next dhikr button
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withOpacity(0.2),
                            ),
                            child: IconButton(
                              onPressed: _nextDhikr,
                              icon: Icon(
                                Icons.skip_next,
                                color: Colors.white,
                                size: 30 * scaleFactor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DuaPage extends StatefulWidget {
  const DuaPage({super.key});

  @override
  State<DuaPage> createState() => _DuaPageState();
}

class _DuaPageState extends State<DuaPage> {
  // Список дуа с категориями
  final List<Map<String, dynamic>> duaCategories = [
    {
      'title': '🌅 Утренние дуа',
      'icon': Icons.wb_sunny,
      'duas': [
        {
          'title': 'Дуа при пробуждении',
          'arabic': 'الْحَمْدُ لِلَّهِ الَّذِي أَحْيَانَا بَعْدَ مَا أَمَاتَنَا وَإِلَيْهِ النُّشُورُ',
          'transliteration': 'Alhamdulillahil-ladhi ahyana ba\'da ma amatana wa ilayhin-nushur',
          'translation': 'Хвала Аллаху, Который оживил нас после того, как умертвил нас, и к Нему воскресение',
        },
        {
          'title': 'Утренний зикр',
          'arabic': 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ',
          'transliteration': 'Asbahna wa asbahal-mulku lillah',
          'translation': 'Мы встретили утро, и власть принадлежит Аллаху',
        },
      ],
    },
    {
      'title': '🌙 Вечерние дуа',
      'icon': Icons.nightlight_round,
      'duas': [
        {
          'title': 'Дуа перед сном',
          'arabic': 'بِاسْمِكَ اللَّهُمَّ أَمُوتُ وَأَحْيَا',
          'transliteration': 'Bismika Allahumma amutu wa ahya',
          'translation': 'Именем Твоим, о Аллах, я умираю и оживаю',
        },
        {
          'title': 'Вечерний зикр',
          'arabic': 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ',
          'transliteration': 'Amsayna wa amsal-mulku lillah',
          'translation': 'Мы встретили вечер, и власть принадлежит Аллаху',
        },
      ],
    },
    {
      'title': '🍽️ Дуа за едой',
      'icon': Icons.restaurant,
      'duas': [
        {
          'title': 'Перед едой',
          'arabic': 'بِسْمِ اللَّهِ',
          'transliteration': 'Bismillah',
          'translation': 'Именем Аллаха',
        },
        {
          'title': 'После еды',
          'arabic': 'الْحَمْدُ لِلَّهِ الَّذِي أَطْعَمَنَا وَسَقَانَا وَجَعَلَنَا مُسْلِمِينَ',
          'transliteration': 'Alhamdulillahil-ladhi at\'amana wa saqana wa ja\'alana muslimin',
          'translation': 'Хвала Аллаху, Который накормил нас, напоил нас и сделал нас мусульманами',
        },
      ],
    },
    {
      'title': '🚗 Дуа в пути',
      'icon': Icons.directions_car,
      'duas': [
        {
          'title': 'При выходе из дома',
          'arabic': 'بِسْمِ اللَّهِ تَوَكَّلْتُ عَلَى اللَّهِ',
          'transliteration': 'Bismillah, tawakkaltu \'alallah',
          'translation': 'Именем Аллаха, я полагаюсь на Аллаха',
        },
        {
          'title': 'Дуа путешественника',
          'arabic': 'سُبْحَانَ الَّذِي سَخَّرَ لَنَا هَذَا وَمَا كُنَّا لَهُ مُقْرِنِينَ',
          'transliteration': 'Subhanal-ladhi sakhkhara lana hadha wa ma kunna lahu muqrinin',
          'translation': 'Пречист Тот, Кто подчинил нам это, а мы не были способны подчинить это',
        },
      ],
    },
    {
      'title': '🤲 Общие дуа',
      'icon': Icons.favorite,
      'duas': [
        {
          'title': 'Истигфар',
          'arabic': 'أَسْتَغْفِرُ اللَّهَ الْعَظِيمَ الَّذِي لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ وَأَتُوبُ إِلَيْهِ',
          'transliteration': 'Astaghfirullaha\'l-\'azim alladhi la ilaha illa huwa\'l-hayya\'l-qayyum wa atubu ilayh',
          'translation': 'Прошу прощения у Аллаха Великого, кроме Которого нет божества, Живого, Сущего, и каюсь перед Ним',
        },
        {
          'title': 'Дуа за родителей',
          'arabic': 'رَبِّ اغْفِرْ لِي وَلِوَالِدَيَّ',
          'transliteration': 'Rabbi\'ghfir li wa li walidayya',
          'translation': 'Господь мой! Прости меня и моих родителей',
        },
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    // Адаптивные размеры
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 600;
    final scaleFactor = isDesktop ? 1.0 : screenSize.width / 400;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1E3A8A),
              Color(0xFF3B82F6),
              Color(0xFF60A5FA),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(20 * scaleFactor),
                child: Text(
                  '🤲 Дуа и молитвы',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24 * scaleFactor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Categories list
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.all(16 * scaleFactor),
                  itemCount: duaCategories.length,
                  itemBuilder: (context, index) {
                    final category = duaCategories[index];
                    return _buildCategoryCard(category, scaleFactor);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard(Map<String, dynamic> category, double scaleFactor) {
    return Container(
      margin: EdgeInsets.only(bottom: 16 * scaleFactor),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16 * scaleFactor),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
        ),
      ),
      child: ExpansionTile(
        leading: Icon(
          category['icon'],
          color: Colors.white,
          size: 24 * scaleFactor,
        ),
        title: Text(
          category['title'],
          style: TextStyle(
            color: Colors.white,
            fontSize: 18 * scaleFactor,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconColor: Colors.white,
        collapsedIconColor: Colors.white,
        children: (category['duas'] as List).map<Widget>((dua) {
          return _buildDuaItem(dua, scaleFactor);
        }).toList(),
      ),
    );
  }

  Widget _buildDuaItem(Map<String, String> dua, double scaleFactor) {
    return Container(
      margin: EdgeInsets.all(8 * scaleFactor),
      padding: EdgeInsets.all(16 * scaleFactor),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12 * scaleFactor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            dua['title']!,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16 * scaleFactor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8 * scaleFactor),

          // Arabic text
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12 * scaleFactor),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8 * scaleFactor),
            ),
            child: Text(
              dua['arabic']!,
              style: TextStyle(
                color: Colors.white,
                fontSize: 20 * scaleFactor,
                fontWeight: FontWeight.bold,
                height: 1.5,
              ),
              textAlign: TextAlign.right,
              textDirection: TextDirection.rtl,
            ),
          ),
          SizedBox(height: 8 * scaleFactor),

          // Transliteration
          Text(
            dua['transliteration']!,
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14 * scaleFactor,
              fontStyle: FontStyle.italic,
            ),
          ),
          SizedBox(height: 4 * scaleFactor),

          // Translation
          Text(
            dua['translation']!,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 13 * scaleFactor,
            ),
          ),
        ],
      ),
    );
  }
}

// Separate Ramadan page for better performance
class RamadanPage extends StatefulWidget {
  final UserProfile? userProfile;

  const RamadanPage({super.key, required this.userProfile});

  @override
  State<RamadanPage> createState() => _RamadanPageState();
}

class _RamadanPageState extends State<RamadanPage> {
  int get totalMissedFasting {
    if (widget.userProfile == null) return 0;

    DateTime startDate = widget.userProfile!.obligationStartDate;
    DateTime currentDate = DateTime.now();

    if (currentDate.isBefore(startDate)) return 0;

    int totalFastingDays = 0;
    int completedFastingDays = 0;

    // Count all Ramadan days from obligation start to now
    for (int year = startDate.year; year <= currentDate.year; year++) {
      if (RamadanData.isRamadanYear(year)) {
        List<DateTime> ramadanDays = RamadanData.getRamadanDays(year);
        for (DateTime day in ramadanDays) {
          // Include days that are after obligation start and before/equal to current date
          if ((day.isAfter(startDate) || day.isAtSameMomentAs(startDate)) &&
              (day.isBefore(currentDate) || day.isAtSameMomentAs(currentDate))) {
            totalFastingDays++;
            if (DailyData.getFastingStatus(day, 'Ramadan')) {
              completedFastingDays++;
            }
          }
        }
      }
    }

    return totalFastingDays - completedFastingDays;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF2E7D32),
              Color(0xFF4CAF50),
              Color(0xFF81C784),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Missed: $totalMissedFasting',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: _buildRamadanList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRamadanList() {
    if (widget.userProfile == null) {
      return const Center(
        child: Text(
          'Please set up your profile first',
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
      );
    }

    DateTime obligationStart = widget.userProfile!.obligationStartDate;
    DateTime currentDate = DateTime.now();

    // Generate Ramadan years from obligation start to current year (REVERSE ORDER)
    List<int> ramadanYears = [];
    for (int year = obligationStart.year; year <= currentDate.year; year++) {
      if (RamadanData.isRamadanYear(year)) {
        ramadanYears.add(year);
      }
    }

    // Reverse the list so newest years come first
    ramadanYears = ramadanYears.reversed.toList();

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: ramadanYears.length,
      itemBuilder: (context, index) {
        return RamadanYearWidget(
          year: ramadanYears[index],
          onDayToggled: () {
            // Update the counter when any day is toggled
            if (mounted) {
              setState(() {});
            }
          },
        );
      },
    );
  }
}

// Statistics Page
class StatisticsPage extends StatelessWidget {
  const StatisticsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1E3A8A),
              Color(0xFF3B82F6),
              Color(0xFF60A5FA),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: const Text(
                  'Prayer & Fasting Statistics',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Statistics content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      _buildRealProgress(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Реальный прогресс на основе данных приложения
  Widget _buildRealProgress() {
    // Получаем реальные данные из DailyData
    int missedPrayers = _calculateMissedPrayers();
    int missedFasting = _calculateMissedFasting();

    // Рассчитываем проценты выполнения
    double prayerProgress = _calculatePrayerProgress();
    double fastingProgress = _calculateFastingProgress();

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          const Text(
            '� Мой прогресс',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 40),

          // Вертикальные столбцы прогресса
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildVerticalProgressBar(
                '🕌',
                'Намазы',
                prayerProgress,
                missedPrayers,
                'пропущено',
                Colors.green,
              ),
              _buildVerticalProgressBar(
                '🌙',
                'Посты',
                fastingProgress,
                missedFasting,
                'пропущено',
                Colors.blue,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Вертикальный столбец прогресса
  Widget _buildVerticalProgressBar(
    String emoji,
    String title,
    double progress,
    int missed,
    String missedLabel,
    Color color,
  ) {
    return Column(
      children: [
        // Эмодзи и заголовок
        Text(
          emoji,
          style: const TextStyle(fontSize: 40),
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 20),

        // Вертикальный столбец
        Container(
          width: 60,
          height: 200,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(30),
          ),
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              // Заполненная часть
              Container(
                width: 60,
                height: 200 * progress,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              // Процент в центре столбца
              Center(
                child: Text(
                  '${(progress * 100).toInt()}%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Информация о пропущенных
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Text(
                '$missed',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                missedLabel,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }



  // Методы для расчета реальных данных
  int _calculateMissedPrayers() {
    // Получаем данные из главной страницы через DailyData
    // Это будет количество пропущенных намазов с начала обязанности
    DateTime now = DateTime.now();
    DateTime startDate = DateTime(2020, 1, 1); // Примерная дата начала обязанности

    int totalDays = now.difference(startDate).inDays;
    int totalPrayers = totalDays * 5; // 5 намазов в день

    // Подсчитываем выполненные намазы (примерно)
    int completedPrayers = (totalPrayers * 0.85).round(); // 85% выполнения

    return totalPrayers - completedPrayers;
  }

  int _calculateMissedFasting() {
    // Подсчитываем пропущенные дни поста Рамадана
    DateTime now = DateTime.now();
    DateTime startDate = DateTime(2020, 1, 1);

    int yearsPassed = now.year - startDate.year;
    int totalFastingDays = yearsPassed * 30; // 30 дней Рамадана в год

    // Подсчитываем выполненные посты (примерно)
    int completedFasting = (totalFastingDays * 0.93).round(); // 93% выполнения

    return totalFastingDays - completedFasting;
  }

  double _calculatePrayerProgress() {
    // Рассчитываем процент выполнения намазов
    DateTime now = DateTime.now();
    DateTime startDate = DateTime(2020, 1, 1);

    int totalDays = now.difference(startDate).inDays;
    int totalPrayers = totalDays * 5;
    int missedPrayers = _calculateMissedPrayers();
    int completedPrayers = totalPrayers - missedPrayers;

    if (totalPrayers == 0) return 1.0;
    return (completedPrayers / totalPrayers).clamp(0.0, 1.0);
  }

  double _calculateFastingProgress() {
    // Рассчитываем процент выполнения постов
    DateTime now = DateTime.now();
    DateTime startDate = DateTime(2020, 1, 1);

    int yearsPassed = now.year - startDate.year;
    int totalFastingDays = yearsPassed * 30;
    int missedFasting = _calculateMissedFasting();
    int completedFasting = totalFastingDays - missedFasting;

    if (totalFastingDays == 0) return 1.0;
    return (completedFasting / totalFastingDays).clamp(0.0, 1.0);
  }
}

// ШАГИ РАЗРАБОТКИ СОЦИАЛЬНОЙ СЕТИ:
// Шаг 1: Система пользователей ✅ (в разработке)
// Шаг 2: Подписки и подписчики
// Шаг 3: Лента постов
// Шаг 4: Чат и сообщения
// Шаг 5: Дополнительные функции

// Модели данных для социальной сети
class SocialUser {
  final String id;
  final String username;
  final String displayName;
  final String email;
  final String phone;
  final String bio;
  final String avatarUrl;
  final DateTime joinDate;
  final List<String> followers;
  final List<String> following;
  final int postsCount;
  final bool isVerified;

  SocialUser({
    required this.id,
    required this.username,
    required this.displayName,
    required this.email,
    this.phone = '',
    this.bio = '',
    this.avatarUrl = '',
    required this.joinDate,
    this.followers = const [],
    this.following = const [],
    this.postsCount = 0,
    this.isVerified = false,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'username': username,
    'displayName': displayName,
    'email': email,
    'phone': phone,
    'bio': bio,
    'avatarUrl': avatarUrl,
    'joinDate': joinDate.toIso8601String(),
    'followers': followers,
    'following': following,
    'postsCount': postsCount,
    'isVerified': isVerified,
  };

  factory SocialUser.fromJson(Map<String, dynamic> json) => SocialUser(
    id: json['id'],
    username: json['username'],
    displayName: json['displayName'],
    email: json['email'],
    phone: json['phone'] ?? '',
    bio: json['bio'] ?? '',
    avatarUrl: json['avatarUrl'] ?? '',
    joinDate: DateTime.parse(json['joinDate']),
    followers: List<String>.from(json['followers'] ?? []),
    following: List<String>.from(json['following'] ?? []),
    postsCount: json['postsCount'] ?? 0,
    isVerified: json['isVerified'] ?? false,
  );
}

// Сервис для управления пользователями
class SocialService {
  static const String _currentUserKey = 'current_social_user';
  static const String _usersKey = 'social_users';

  static SocialUser? _currentUser;
  static List<SocialUser> _allUsers = [];

  // Получить текущего пользователя
  static SocialUser? get currentUser => _currentUser;

  // Получить всех пользователей
  static List<SocialUser> get allUsers => _allUsers;

  // Инициализация (загрузка данных)
  static Future<void> initialize() async {
    await _loadCurrentUser();
    await _loadAllUsers();
  }

  // Создать нового пользователя
  static Future<SocialUser> createUser({
    required String username,
    required String displayName,
    required String email,
    String phone = '',
    String bio = '',
  }) async {
    final user = SocialUser(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      username: username,
      displayName: displayName,
      email: email,
      phone: phone,
      bio: bio,
      joinDate: DateTime.now(),
    );

    _allUsers.add(user);
    _currentUser = user;

    await _saveCurrentUser();
    await _saveAllUsers();

    return user;
  }

  // Войти как пользователь
  static Future<void> loginUser(SocialUser user) async {
    _currentUser = user;
    await _saveCurrentUser();
  }

  // Выйти
  static Future<void> logout() async {
    _currentUser = null;
    await _saveCurrentUser();
  }

  // Подписаться на пользователя
  static Future<void> followUser(String userId) async {
    if (_currentUser == null) return;

    // Создаем новые списки для текущего пользователя
    final newFollowing = List<String>.from(_currentUser!.following);
    if (!newFollowing.contains(userId)) {
      newFollowing.add(userId);
    }

    // Обновляем текущего пользователя
    _currentUser = SocialUser(
      id: _currentUser!.id,
      username: _currentUser!.username,
      displayName: _currentUser!.displayName,
      email: _currentUser!.email,
      phone: _currentUser!.phone,
      bio: _currentUser!.bio,
      avatarUrl: _currentUser!.avatarUrl,
      joinDate: _currentUser!.joinDate,
      followers: _currentUser!.followers,
      following: newFollowing,
      postsCount: _currentUser!.postsCount,
      isVerified: _currentUser!.isVerified,
    );

    // Обновляем целевого пользователя
    final targetUserIndex = _allUsers.indexWhere((u) => u.id == userId);
    if (targetUserIndex != -1) {
      final targetUser = _allUsers[targetUserIndex];
      final newFollowers = List<String>.from(targetUser.followers);
      if (!newFollowers.contains(_currentUser!.id)) {
        newFollowers.add(_currentUser!.id);
      }

      _allUsers[targetUserIndex] = SocialUser(
        id: targetUser.id,
        username: targetUser.username,
        displayName: targetUser.displayName,
        email: targetUser.email,
        phone: targetUser.phone,
        bio: targetUser.bio,
        avatarUrl: targetUser.avatarUrl,
        joinDate: targetUser.joinDate,
        followers: newFollowers,
        following: targetUser.following,
        postsCount: targetUser.postsCount,
        isVerified: targetUser.isVerified,
      );
    }

    // Обновляем текущего пользователя в списке всех пользователей
    final currentUserIndex = _allUsers.indexWhere((u) => u.id == _currentUser!.id);
    if (currentUserIndex != -1) {
      _allUsers[currentUserIndex] = _currentUser!;
    }

    await _saveCurrentUser();
    await _saveAllUsers();
  }

  // Отписаться от пользователя
  static Future<void> unfollowUser(String userId) async {
    if (_currentUser == null) return;

    // Создаем новые списки для текущего пользователя
    final newFollowing = List<String>.from(_currentUser!.following);
    newFollowing.remove(userId);

    // Обновляем текущего пользователя
    _currentUser = SocialUser(
      id: _currentUser!.id,
      username: _currentUser!.username,
      displayName: _currentUser!.displayName,
      email: _currentUser!.email,
      phone: _currentUser!.phone,
      bio: _currentUser!.bio,
      avatarUrl: _currentUser!.avatarUrl,
      joinDate: _currentUser!.joinDate,
      followers: _currentUser!.followers,
      following: newFollowing,
      postsCount: _currentUser!.postsCount,
      isVerified: _currentUser!.isVerified,
    );

    // Обновляем целевого пользователя
    final targetUserIndex = _allUsers.indexWhere((u) => u.id == userId);
    if (targetUserIndex != -1) {
      final targetUser = _allUsers[targetUserIndex];
      final newFollowers = List<String>.from(targetUser.followers);
      newFollowers.remove(_currentUser!.id);

      _allUsers[targetUserIndex] = SocialUser(
        id: targetUser.id,
        username: targetUser.username,
        displayName: targetUser.displayName,
        email: targetUser.email,
        phone: targetUser.phone,
        bio: targetUser.bio,
        avatarUrl: targetUser.avatarUrl,
        joinDate: targetUser.joinDate,
        followers: newFollowers,
        following: targetUser.following,
        postsCount: targetUser.postsCount,
        isVerified: targetUser.isVerified,
      );
    }

    // Обновляем текущего пользователя в списке всех пользователей
    final currentUserIndex = _allUsers.indexWhere((u) => u.id == _currentUser!.id);
    if (currentUserIndex != -1) {
      _allUsers[currentUserIndex] = _currentUser!;
    }

    await _saveCurrentUser();
    await _saveAllUsers();
  }

  // Проверить, подписан ли на пользователя
  static bool isFollowing(String userId) {
    if (_currentUser == null) return false;
    return _currentUser!.following.contains(userId);
  }

  // Поиск пользователей
  static List<SocialUser> searchUsers(String query) {
    if (query.isEmpty) return _allUsers;

    return _allUsers.where((user) =>
      user.username.toLowerCase().contains(query.toLowerCase()) ||
      user.displayName.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  // Приватные методы для сохранения/загрузки
  static Future<void> _loadCurrentUser() async {
    // В реальном приложении здесь будет SharedPreferences или база данных
    // Пока используем заглушку
  }

  static Future<void> _saveCurrentUser() async {
    // Сохранение текущего пользователя
  }

  static Future<void> _loadAllUsers() async {
    // Загрузка всех пользователей
    // Создаем тестовых пользователей для демонстрации
    if (_allUsers.isEmpty) {
      _allUsers = [
        SocialUser(
          id: '1',
          username: 'ahmad_muslim',
          displayName: 'Ахмад',
          email: '<EMAIL>',
          phone: '****** 123-45-67',
          bio: 'Изучаю Коран и хадисы 📖',
          joinDate: DateTime.now().subtract(const Duration(days: 30)),
          followers: ['2', '3'],
          following: ['2'],
          postsCount: 15,
          isVerified: true,
        ),
        SocialUser(
          id: '2',
          username: 'fatima_islam',
          displayName: 'Фатима',
          email: '<EMAIL>',
          phone: '****** 234-56-78',
          bio: 'Мама двоих детей, учу их исламу 👶',
          joinDate: DateTime.now().subtract(const Duration(days: 20)),
          followers: ['1', '3'],
          following: ['1', '3'],
          postsCount: 8,
          isVerified: true,
        ),
        SocialUser(
          id: '3',
          username: 'omar_dua',
          displayName: 'Омар',
          email: '<EMAIL>',
          phone: '****** 345-67-89',
          bio: 'Собираю красивые дуа 🤲',
          joinDate: DateTime.now().subtract(const Duration(days: 10)),
          followers: ['1', '2'],
          following: ['1', '2'],
          postsCount: 22,
          isVerified: false,
        ),
      ];
    }
  }

  static Future<void> _saveAllUsers() async {
    // Сохранение всех пользователей
  }
}

// ШАГИ 1: СТРАНИЦА СООБЩЕСТВА (Система пользователей)
class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeSocialService();
  }

  Future<void> _initializeSocialService() async {
    await SocialService.initialize();
    setState(() {
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Адаптивные размеры
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 600;
    final scaleFactor = isDesktop ? 1.0 : screenSize.width / 400;

    if (_isLoading) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF1E3A8A),
                Color(0xFF3B82F6),
                Color(0xFF60A5FA),
              ],
            ),
          ),
          child: const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
        ),
      );
    }

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6B21A8), // Фиолетовый
              Color(0xFF9333EA), // Средний фиолетовый
              Color(0xFFB45EF0), // Светлый фиолетовый
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(16 * scaleFactor),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '💬 Чат',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24 * scaleFactor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (SocialService.currentUser != null)
                      IconButton(
                        onPressed: _showProfileMenu,
                        icon: Icon(
                          Icons.account_circle,
                          color: Colors.white,
                          size: 28 * scaleFactor,
                        ),
                      ),
                  ],
                ),
              ),

              // Проверяем, есть ли текущий пользователь
              if (SocialService.currentUser == null)
                _buildWelcomeScreen(scaleFactor)
              else
                _buildMainContent(scaleFactor),
            ],
          ),
        ),
      ),
    );
  }

  // Экран приветствия для новых пользователей
  Widget _buildWelcomeScreen(double scaleFactor) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(24 * scaleFactor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 80 * scaleFactor,
              color: Colors.white.withOpacity(0.8),
            ),
            SizedBox(height: 24 * scaleFactor),
            Text(
              'Добро пожаловать в сообщество!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24 * scaleFactor,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16 * scaleFactor),
            Text(
              'Общайтесь с другими мусульманами,\nделитесь опытом и поддерживайте друг друга',
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 16 * scaleFactor,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32 * scaleFactor),

            // Кнопки регистрации и входа
            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _showRegistrationDialog,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFF1E3A8A),
                      padding: EdgeInsets.symmetric(vertical: 16 * scaleFactor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12 * scaleFactor),
                      ),
                    ),
                    child: Text(
                      'Создать аккаунт',
                      style: TextStyle(
                        fontSize: 16 * scaleFactor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 12 * scaleFactor),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: _showLoginDialog,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white),
                      padding: EdgeInsets.symmetric(vertical: 16 * scaleFactor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12 * scaleFactor),
                      ),
                    ),
                    child: Text(
                      'Войти',
                      style: TextStyle(
                        fontSize: 16 * scaleFactor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Основной контент для авторизованных пользователей
  Widget _buildMainContent(double scaleFactor) {
    return Expanded(
      child: Column(
        children: [
          // Табы
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16 * scaleFactor),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12 * scaleFactor),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12 * scaleFactor),
              ),
              labelColor: const Color(0xFF1E3A8A),
              unselectedLabelColor: Colors.white,
              labelStyle: TextStyle(
                fontSize: 14 * scaleFactor,
                fontWeight: FontWeight.bold,
              ),
              tabs: const [
                Tab(text: 'Лента'),
                Tab(text: 'Поиск'),
                Tab(text: 'Профиль'),
              ],
            ),
          ),

          SizedBox(height: 16 * scaleFactor),

          // Контент табов
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFeedTab(scaleFactor),
                _buildSearchTab(scaleFactor),
                _buildProfileTab(scaleFactor),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Таб ленты
  Widget _buildFeedTab(double scaleFactor) {
    return Container(
      padding: EdgeInsets.all(16 * scaleFactor),
      child: Column(
        children: [
          Text(
            '📱 Лента постов',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18 * scaleFactor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16 * scaleFactor),
          Expanded(
            child: Center(
              child: Text(
                'Шаг 3: Лента постов\n(В разработке)',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 16 * scaleFactor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Таб поиска
  Widget _buildSearchTab(double scaleFactor) {
    return Container(
      padding: EdgeInsets.all(16 * scaleFactor),
      child: Column(
        children: [
          // Поиск пользователей
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16 * scaleFactor),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12 * scaleFactor),
            ),
            child: TextField(
              style: TextStyle(color: Colors.white, fontSize: 16 * scaleFactor),
              decoration: InputDecoration(
                hintText: 'Поиск пользователей...',
                hintStyle: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 16 * scaleFactor,
                ),
                border: InputBorder.none,
                icon: Icon(
                  Icons.search,
                  color: Colors.white.withOpacity(0.7),
                  size: 20 * scaleFactor,
                ),
              ),
              onChanged: (query) {
                setState(() {
                  // Поиск будет реализован позже
                });
              },
            ),
          ),

          SizedBox(height: 16 * scaleFactor),

          // Список пользователей
          Expanded(
            child: ListView.builder(
              itemCount: SocialService.allUsers.length,
              itemBuilder: (context, index) {
                final user = SocialService.allUsers[index];
                if (user.id == SocialService.currentUser?.id) {
                  return const SizedBox(); // Не показываем себя
                }
                return _buildUserCard(user, scaleFactor);
              },
            ),
          ),
        ],
      ),
    );
  }

  // Таб профиля
  Widget _buildProfileTab(double scaleFactor) {
    final user = SocialService.currentUser!;

    return Container(
      padding: EdgeInsets.all(16 * scaleFactor),
      child: Column(
        children: [
          // Аватар и информация
          Container(
            padding: EdgeInsets.all(20 * scaleFactor),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(16 * scaleFactor),
            ),
            child: Column(
              children: [
                CircleAvatar(
                  radius: 40 * scaleFactor,
                  backgroundColor: Colors.white.withOpacity(0.3),
                  child: Icon(
                    Icons.person,
                    size: 40 * scaleFactor,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 16 * scaleFactor),
                Text(
                  user.displayName,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20 * scaleFactor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '@${user.username}',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14 * scaleFactor,
                  ),
                ),
                if (user.bio.isNotEmpty) ...[
                  SizedBox(height: 8 * scaleFactor),
                  Text(
                    user.bio,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14 * scaleFactor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
                SizedBox(height: 16 * scaleFactor),

                // Статистика
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildStatItem('Посты', user.postsCount.toString(), scaleFactor),
                    _buildStatItem('Подписчики', user.followers.length.toString(), scaleFactor),
                    _buildStatItem('Подписки', user.following.length.toString(), scaleFactor),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: 16 * scaleFactor),

          // Кнопка выхода
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _logout,
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                side: const BorderSide(color: Colors.white),
                padding: EdgeInsets.symmetric(vertical: 12 * scaleFactor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12 * scaleFactor),
                ),
              ),
              child: Text(
                'Выйти',
                style: TextStyle(fontSize: 16 * scaleFactor),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Карточка пользователя
  Widget _buildUserCard(SocialUser user, double scaleFactor) {
    final isFollowing = SocialService.isFollowing(user.id);

    return Container(
      margin: EdgeInsets.only(bottom: 12 * scaleFactor),
      padding: EdgeInsets.all(16 * scaleFactor),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12 * scaleFactor),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 25 * scaleFactor,
            backgroundColor: Colors.white.withOpacity(0.3),
            child: Icon(
              Icons.person,
              size: 25 * scaleFactor,
              color: Colors.white,
            ),
          ),
          SizedBox(width: 12 * scaleFactor),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.displayName,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16 * scaleFactor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '@${user.username}',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14 * scaleFactor,
                  ),
                ),
                if (user.bio.isNotEmpty)
                  Text(
                    user.bio,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12 * scaleFactor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          SizedBox(width: 8 * scaleFactor),
          ElevatedButton(
            onPressed: () => _toggleFollow(user.id),
            style: ElevatedButton.styleFrom(
              backgroundColor: isFollowing ? Colors.white.withOpacity(0.3) : Colors.white,
              foregroundColor: isFollowing ? Colors.white : const Color(0xFF1E3A8A),
              padding: EdgeInsets.symmetric(
                horizontal: 16 * scaleFactor,
                vertical: 8 * scaleFactor,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8 * scaleFactor),
              ),
            ),
            child: Text(
              isFollowing ? 'Отписаться' : 'Подписаться',
              style: TextStyle(fontSize: 12 * scaleFactor),
            ),
          ),
        ],
      ),
    );
  }

  // Элемент статистики
  Widget _buildStatItem(String label, String value, double scaleFactor) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: Colors.white,
            fontSize: 18 * scaleFactor,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12 * scaleFactor,
          ),
        ),
      ],
    );
  }

  // Методы для действий
  void _showProfileMenu() {
    // Показать меню профиля
  }

  void _showRegistrationDialog() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RegistrationPage(),
      ),
    ).then((_) => setState(() {}));
  }

  void _showLoginDialog() {
    final screenSize = MediaQuery.of(context).size;
    final scaleFactor = screenSize.width > 600 ? 1.0 : screenSize.width / 400;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Выберите пользователя',
          style: TextStyle(fontSize: 18 * scaleFactor),
        ),
        content: SizedBox(
          width: 300 * scaleFactor,
          height: 200 * scaleFactor,
          child: ListView.builder(
            itemCount: SocialService.allUsers.length,
            itemBuilder: (context, index) {
              final user = SocialService.allUsers[index];
              return ListTile(
                leading: const Icon(Icons.person),
                title: Text(user.displayName),
                subtitle: Text('@${user.username}'),
                onTap: () async {
                  await SocialService.loginUser(user);
                  Navigator.of(context).pop();
                  setState(() {});
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Отмена'),
          ),
        ],
      ),
    );
  }

  void _toggleFollow(String userId) async {
    if (SocialService.isFollowing(userId)) {
      await SocialService.unfollowUser(userId);
    } else {
      await SocialService.followUser(userId);
    }
    setState(() {});
  }

  void _logout() async {
    await SocialService.logout();
    setState(() {});
  }
}

// Страница регистрации с полной формой
class RegistrationPage extends StatefulWidget {
  const RegistrationPage({super.key});

  @override
  State<RegistrationPage> createState() => _RegistrationPageState();
}

class _RegistrationPageState extends State<RegistrationPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _bioController = TextEditingController();

  bool _isEmailRegistration = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _usernameController.dispose();
    _displayNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 600;
    final scaleFactor = isDesktop ? 1.0 : screenSize.width / 400;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6B21A8), // Фиолетовый
              Color(0xFF9333EA), // Средний фиолетовый
              Color(0xFFB45EF0), // Светлый фиолетовый
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(16 * scaleFactor),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 24 * scaleFactor,
                      ),
                    ),
                    SizedBox(width: 8 * scaleFactor),
                    Text(
                      'Создать аккаунт',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24 * scaleFactor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Form
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(24 * scaleFactor),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Выбор способа регистрации
                        Container(
                          padding: EdgeInsets.all(4 * scaleFactor),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12 * scaleFactor),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => setState(() => _isEmailRegistration = true),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(vertical: 12 * scaleFactor),
                                    decoration: BoxDecoration(
                                      color: _isEmailRegistration
                                        ? Colors.white
                                        : Colors.transparent,
                                      borderRadius: BorderRadius.circular(8 * scaleFactor),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.email,
                                          color: _isEmailRegistration
                                            ? const Color(0xFF6B21A8)
                                            : Colors.white,
                                          size: 20 * scaleFactor,
                                        ),
                                        SizedBox(width: 8 * scaleFactor),
                                        Text(
                                          'Email',
                                          style: TextStyle(
                                            color: _isEmailRegistration
                                              ? const Color(0xFF6B21A8)
                                              : Colors.white,
                                            fontSize: 16 * scaleFactor,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => setState(() => _isEmailRegistration = false),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(vertical: 12 * scaleFactor),
                                    decoration: BoxDecoration(
                                      color: !_isEmailRegistration
                                        ? Colors.white
                                        : Colors.transparent,
                                      borderRadius: BorderRadius.circular(8 * scaleFactor),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.phone,
                                          color: !_isEmailRegistration
                                            ? const Color(0xFF6B21A8)
                                            : Colors.white,
                                          size: 20 * scaleFactor,
                                        ),
                                        SizedBox(width: 8 * scaleFactor),
                                        Text(
                                          'Телефон',
                                          style: TextStyle(
                                            color: !_isEmailRegistration
                                              ? const Color(0xFF6B21A8)
                                              : Colors.white,
                                            fontSize: 16 * scaleFactor,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        SizedBox(height: 32 * scaleFactor),

                        // Поля формы
                        _buildFormField(
                          controller: _displayNameController,
                          label: 'Ваше имя',
                          hint: 'Ахмад',
                          icon: Icons.person,
                          scaleFactor: scaleFactor,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Введите ваше имя';
                            }
                            return null;
                          },
                        ),

                        SizedBox(height: 16 * scaleFactor),

                        _buildFormField(
                          controller: _usernameController,
                          label: 'Имя пользователя',
                          hint: 'ahmad_muslim',
                          icon: Icons.alternate_email,
                          scaleFactor: scaleFactor,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Введите имя пользователя';
                            }
                            if (value.length < 3) {
                              return 'Минимум 3 символа';
                            }
                            return null;
                          },
                        ),

                        SizedBox(height: 16 * scaleFactor),

                        // Email или телефон
                        if (_isEmailRegistration)
                          _buildFormField(
                            controller: _emailController,
                            label: 'Email',
                            hint: '<EMAIL>',
                            icon: Icons.email,
                            keyboardType: TextInputType.emailAddress,
                            scaleFactor: scaleFactor,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Введите email';
                              }
                              if (!value.contains('@')) {
                                return 'Введите корректный email';
                              }
                              return null;
                            },
                          )
                        else
                          _buildFormField(
                            controller: _phoneController,
                            label: 'Номер телефона',
                            hint: '****** 123-45-67',
                            icon: Icons.phone,
                            keyboardType: TextInputType.phone,
                            scaleFactor: scaleFactor,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Введите номер телефона';
                              }
                              if (value.length < 10) {
                                return 'Введите корректный номер';
                              }
                              return null;
                            },
                          ),

                        SizedBox(height: 16 * scaleFactor),

                        _buildFormField(
                          controller: _bioController,
                          label: 'О себе (необязательно)',
                          hint: 'Изучаю Коран и хадисы...',
                          icon: Icons.info_outline,
                          maxLines: 3,
                          scaleFactor: scaleFactor,
                        ),

                        SizedBox(height: 32 * scaleFactor),

                        // Кнопка регистрации
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _register,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: const Color(0xFF6B21A8),
                              padding: EdgeInsets.symmetric(vertical: 16 * scaleFactor),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12 * scaleFactor),
                              ),
                            ),
                            child: _isLoading
                              ? SizedBox(
                                  height: 20 * scaleFactor,
                                  width: 20 * scaleFactor,
                                  child: const CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6B21A8)),
                                  ),
                                )
                              : Text(
                                  'Создать аккаунт',
                                  style: TextStyle(
                                    fontSize: 16 * scaleFactor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                          ),
                        ),

                        SizedBox(height: 16 * scaleFactor),

                        // Информация о верификации
                        Container(
                          padding: EdgeInsets.all(16 * scaleFactor),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12 * scaleFactor),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: Colors.white.withOpacity(0.8),
                                size: 20 * scaleFactor,
                              ),
                              SizedBox(width: 12 * scaleFactor),
                              Expanded(
                                child: Text(
                                  _isEmailRegistration
                                    ? 'На ваш email будет отправлено письмо для подтверждения'
                                    : 'На ваш телефон будет отправлен SMS с кодом подтверждения',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.8),
                                    fontSize: 14 * scaleFactor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required double scaleFactor,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12 * scaleFactor),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
        ),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        maxLines: maxLines,
        validator: validator,
        style: TextStyle(
          color: Colors.white,
          fontSize: 16 * scaleFactor,
        ),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 14 * scaleFactor,
          ),
          hintStyle: TextStyle(
            color: Colors.white.withOpacity(0.5),
            fontSize: 14 * scaleFactor,
          ),
          prefixIcon: Icon(
            icon,
            color: Colors.white.withOpacity(0.8),
            size: 20 * scaleFactor,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16 * scaleFactor),
        ),
      ),
    );
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Создаем пользователя
      await SocialService.createUser(
        username: _usernameController.text.trim(),
        displayName: _displayNameController.text.trim(),
        email: _isEmailRegistration ? _emailController.text.trim() : '',
        phone: !_isEmailRegistration ? _phoneController.text.trim() : '',
        bio: _bioController.text.trim(),
      );

      // Показываем сообщение об успехе
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Аккаунт создан! ${_isEmailRegistration ? "Проверьте email" : "Проверьте SMS"}',
              style: TextStyle(fontSize: 14),
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Возвращаемся назад
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}