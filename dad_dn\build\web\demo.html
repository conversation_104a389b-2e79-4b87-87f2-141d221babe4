<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dad Dn - Prayer & Fasting Tracker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-top: 40px;
        }
        
        .logo {
            font-size: 60px;
            margin-bottom: 10px;
        }
        
        .app-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .app-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card h3 {
            font-size: 18px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .card p {
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .feature-name {
            font-size: 14px;
            font-weight: 500;
        }
        
        .bottom-nav {
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        
        .nav-item:hover {
            opacity: 0.7;
        }
        
        .nav-icon {
            font-size: 20px;
        }
        
        .nav-label {
            font-size: 12px;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🕌</div>
            <div class="app-title">Dad Dn</div>
            <div class="app-subtitle">Prayer & Fasting Tracker</div>
        </div>
        
        <div class="content">
            <div class="card">
                <h3>📊 Missed Prayers & Fasting</h3>
                <p>Track your missed prayers and fasting days. Automatic calculation based on your age and gender.</p>
                <div style="margin-top: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Fajr:</span> <span>0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Dhuhr:</span> <span>0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Asr:</span> <span>0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Maghrib:</span> <span>0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Isha:</span> <span>0</span>
                    </div>
                </div>
            </div>
            
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">⏰</div>
                    <div class="feature-name">Prayer Times</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📖</div>
                    <div class="feature-name">Quran</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📿</div>
                    <div class="feature-name">Tasbih</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">👥</div>
                    <div class="feature-name">Community</div>
                </div>
            </div>
        </div>
        
        <div class="bottom-nav">
            <div class="nav-item">
                <div class="nav-icon">🕌</div>
                <div class="nav-label">Tracker</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">⏰</div>
                <div class="nav-label">Times</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📖</div>
                <div class="nav-label">Quran</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📿</div>
                <div class="nav-label">Tasbih</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">👥</div>
                <div class="nav-label">Community</div>
            </div>
        </div>
        
        <div class="status">
            <strong>✅ Первый шаг завершен!</strong><br>
            Базовая архитектура и навигация настроены.<br>
            Готово к переходу ко второму шагу.
        </div>
    </div>
</body>
</html>
