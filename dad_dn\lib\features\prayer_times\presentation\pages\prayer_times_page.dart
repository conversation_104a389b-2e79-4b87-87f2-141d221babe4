import 'package:flutter/material.dart';

class PrayerTimesPage extends StatelessWidget {
  const PrayerTimesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Prayer Times'),
      ),
      body: const Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Today\'s Prayer Times',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    PrayerTimeRow(prayer: 'Fajr', time: '05:30'),
                    Divider(),
                    PrayerTimeRow(prayer: 'Dhuhr', time: '12:15'),
                    Divider(),
                    PrayerTimeRow(prayer: 'Asr', time: '15:45'),
                    Divider(),
                    PrayerTimeRow(prayer: 'Maghrib', time: '18:20'),
                    Divider(),
                    PrayerTimeRow(prayer: 'Isha', time: '19:50'),
                  ],
                ),
              ),
            ),
            SizedBox(height: 24),
            Text(
              'This is a placeholder for the Prayer Times feature.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            Text(
              'Features to implement:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text('• Automatic location detection'),
            Text('• Manual location input'),
            Text('• Local notifications'),
            Text('• Islamic calendar support'),
            Text('• Different calculation methods'),
          ],
        ),
      ),
    );
  }
}

class PrayerTimeRow extends StatelessWidget {
  final String prayer;
  final String time;

  const PrayerTimeRow({
    super.key,
    required this.prayer,
    required this.time,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          prayer,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          time,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.green,
          ),
        ),
      ],
    );
  }
}
