import 'package:flutter/material.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          const Text(
            'App Settings',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Theme Settings
          Card(
            child: Column(
              children: [
                const ListTile(
                  title: Text(
                    'Appearance',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.palette),
                  title: const Text('Theme'),
                  subtitle: const Text('Light'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: Show theme selector
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.language),
                  title: const Text('Language'),
                  subtitle: const Text('English'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: Show language selector
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Notification Settings
          Card(
            child: Column(
              children: [
                const ListTile(
                  title: Text(
                    'Notifications',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                SwitchListTile(
                  secondary: const Icon(Icons.notifications),
                  title: const Text('Prayer Time Notifications'),
                  subtitle: const Text('Get notified for prayer times'),
                  value: true,
                  onChanged: (bool value) {
                    // TODO: Toggle prayer notifications
                  },
                ),
                SwitchListTile(
                  secondary: const Icon(Icons.book),
                  title: const Text('Daily Verse'),
                  subtitle: const Text('Daily Quran verse notification'),
                  value: true,
                  onChanged: (bool value) {
                    // TODO: Toggle daily verse
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.volume_up),
                  title: const Text('Notification Sound'),
                  subtitle: const Text('Default'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: Show sound selector
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Prayer Settings
          Card(
            child: Column(
              children: [
                const ListTile(
                  title: Text(
                    'Prayer Settings',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.calculate),
                  title: const Text('Calculation Method'),
                  subtitle: const Text('Muslim World League'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: Show calculation methods
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.location_on),
                  title: const Text('Location'),
                  subtitle: const Text('Auto-detect'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: Show location settings
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // App Info
          Card(
            child: Column(
              children: [
                const ListTile(
                  title: Text(
                    'About',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('App Version'),
                  subtitle: const Text('1.0.0'),
                  onTap: () {
                    // TODO: Show version info
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.privacy_tip),
                  title: const Text('Privacy Policy'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: Show privacy policy
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.description),
                  title: const Text('Terms of Service'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // TODO: Show terms
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          const Text(
            'Features to implement:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Text('• Theme switching (light/dark)'),
          const Text('• Multi-language support'),
          const Text('• Notification customization'),
          const Text('• Prayer calculation methods'),
          const Text('• Location settings'),
          const Text('• Data backup/restore'),
        ],
      ),
    );
  }
}
