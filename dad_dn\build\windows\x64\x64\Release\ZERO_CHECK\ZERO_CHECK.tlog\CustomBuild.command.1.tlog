^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DAD\DAD_DN\BUILD\WINDOWS\X64\CMAKEFILES\70CDD584D97C8F93A25B297EC9BBD204\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/dad/dad_dn/windows -BC:/Users/<USER>/OneDrive/Desktop/dad/dad_dn/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/OneDrive/Desktop/dad/dad_dn/build/windows/x64/dad_dn.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
