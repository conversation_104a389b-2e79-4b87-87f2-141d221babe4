{"inputs": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\pubspec.yaml", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\.dart_tool\\flutter_build\\1d059dd60e95fc99635effcef9b16011\\main.dart.js", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\.dart_tool\\flutter_build\\1d059dd60e95fc99635effcef9b16011\\main.dart.js.map", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\pubspec.yaml", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "C:\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.3.1\\LICENSE", "C:\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\flutter\\packages\\flutter\\LICENSE", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD732148317", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\web\\demo.html", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\web\\favicon.png", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\web\\icons\\Icon-192.png", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\web\\icons\\Icon-512.png", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\web\\icons\\Icon-maskable-192.png", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\web\\icons\\Icon-maskable-512.png", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\web\\index.html", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\web\\manifest.json"], "outputs": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\build\\web\\main.dart.js", "C:\\Users\\<USER>\\OneDrive\\Desktop\\dad\\dad_dn\\build\\web\\main.dart.js.map", "build\\web\\assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "build\\web\\assets\\fonts/MaterialIcons-Regular.otf", "build\\web\\assets\\shaders/ink_sparkle.frag", "build\\web\\assets\\AssetManifest.json", "build\\web\\assets\\AssetManifest.bin", "build\\web\\assets\\AssetManifest.bin.json", "build\\web\\assets\\FontManifest.json", "build\\web\\assets\\NOTICES", "build\\web\\demo.html", "build\\web\\favicon.png", "build\\web\\icons\\Icon-192.png", "build\\web\\icons\\Icon-512.png", "build\\web\\icons\\Icon-maskable-192.png", "build\\web\\icons\\Icon-maskable-512.png", "build\\web\\manifest.json"]}