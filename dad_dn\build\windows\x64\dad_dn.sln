﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{EEC85138-0A92-3411-8B5A-B3187B907FBA}"
	ProjectSection(ProjectDependencies) = postProject
		{032FA036-04ED-317D-B520-DBCF027D06EC} = {032FA036-04ED-317D-B520-DBCF027D06EC}
		{79648353-B3FA-3675-816A-139F424EA247} = {79648353-B3FA-3675-816A-139F424EA247}
		{24FA97E2-B236-39FA-8C96-947F1116F6D8} = {24FA97E2-B236-39FA-8C96-947F1116F6D8}
		{B069737F-E4A2-3068-8C9F-3FA248062C58} = {B069737F-E4A2-3068-8C9F-3FA248062C58}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{49B56290-E876-36A5-8F66-C6088BFD4791}"
	ProjectSection(ProjectDependencies) = postProject
		{EEC85138-0A92-3411-8B5A-B3187B907FBA} = {EEC85138-0A92-3411-8B5A-B3187B907FBA}
		{032FA036-04ED-317D-B520-DBCF027D06EC} = {032FA036-04ED-317D-B520-DBCF027D06EC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{032FA036-04ED-317D-B520-DBCF027D06EC}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "dad_dn", "runner\dad_dn.vcxproj", "{79648353-B3FA-3675-816A-139F424EA247}"
	ProjectSection(ProjectDependencies) = postProject
		{032FA036-04ED-317D-B520-DBCF027D06EC} = {032FA036-04ED-317D-B520-DBCF027D06EC}
		{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72} = {4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}
		{24FA97E2-B236-39FA-8C96-947F1116F6D8} = {24FA97E2-B236-39FA-8C96-947F1116F6D8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "flutter\flutter_assemble.vcxproj", "{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}"
	ProjectSection(ProjectDependencies) = postProject
		{032FA036-04ED-317D-B520-DBCF027D06EC} = {032FA036-04ED-317D-B520-DBCF027D06EC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "flutter\flutter_wrapper_app.vcxproj", "{24FA97E2-B236-39FA-8C96-947F1116F6D8}"
	ProjectSection(ProjectDependencies) = postProject
		{032FA036-04ED-317D-B520-DBCF027D06EC} = {032FA036-04ED-317D-B520-DBCF027D06EC}
		{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72} = {4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "flutter\flutter_wrapper_plugin.vcxproj", "{B069737F-E4A2-3068-8C9F-3FA248062C58}"
	ProjectSection(ProjectDependencies) = postProject
		{032FA036-04ED-317D-B520-DBCF027D06EC} = {032FA036-04ED-317D-B520-DBCF027D06EC}
		{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72} = {4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EEC85138-0A92-3411-8B5A-B3187B907FBA}.Debug|x64.ActiveCfg = Debug|x64
		{EEC85138-0A92-3411-8B5A-B3187B907FBA}.Debug|x64.Build.0 = Debug|x64
		{EEC85138-0A92-3411-8B5A-B3187B907FBA}.Profile|x64.ActiveCfg = Profile|x64
		{EEC85138-0A92-3411-8B5A-B3187B907FBA}.Profile|x64.Build.0 = Profile|x64
		{EEC85138-0A92-3411-8B5A-B3187B907FBA}.Release|x64.ActiveCfg = Release|x64
		{EEC85138-0A92-3411-8B5A-B3187B907FBA}.Release|x64.Build.0 = Release|x64
		{49B56290-E876-36A5-8F66-C6088BFD4791}.Debug|x64.ActiveCfg = Debug|x64
		{49B56290-E876-36A5-8F66-C6088BFD4791}.Debug|x64.Build.0 = Debug|x64
		{49B56290-E876-36A5-8F66-C6088BFD4791}.Profile|x64.ActiveCfg = Profile|x64
		{49B56290-E876-36A5-8F66-C6088BFD4791}.Profile|x64.Build.0 = Profile|x64
		{49B56290-E876-36A5-8F66-C6088BFD4791}.Release|x64.ActiveCfg = Release|x64
		{49B56290-E876-36A5-8F66-C6088BFD4791}.Release|x64.Build.0 = Release|x64
		{032FA036-04ED-317D-B520-DBCF027D06EC}.Debug|x64.ActiveCfg = Debug|x64
		{032FA036-04ED-317D-B520-DBCF027D06EC}.Debug|x64.Build.0 = Debug|x64
		{032FA036-04ED-317D-B520-DBCF027D06EC}.Profile|x64.ActiveCfg = Profile|x64
		{032FA036-04ED-317D-B520-DBCF027D06EC}.Profile|x64.Build.0 = Profile|x64
		{032FA036-04ED-317D-B520-DBCF027D06EC}.Release|x64.ActiveCfg = Release|x64
		{032FA036-04ED-317D-B520-DBCF027D06EC}.Release|x64.Build.0 = Release|x64
		{79648353-B3FA-3675-816A-139F424EA247}.Debug|x64.ActiveCfg = Debug|x64
		{79648353-B3FA-3675-816A-139F424EA247}.Debug|x64.Build.0 = Debug|x64
		{79648353-B3FA-3675-816A-139F424EA247}.Profile|x64.ActiveCfg = Profile|x64
		{79648353-B3FA-3675-816A-139F424EA247}.Profile|x64.Build.0 = Profile|x64
		{79648353-B3FA-3675-816A-139F424EA247}.Release|x64.ActiveCfg = Release|x64
		{79648353-B3FA-3675-816A-139F424EA247}.Release|x64.Build.0 = Release|x64
		{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}.Debug|x64.ActiveCfg = Debug|x64
		{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}.Debug|x64.Build.0 = Debug|x64
		{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}.Profile|x64.ActiveCfg = Profile|x64
		{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}.Profile|x64.Build.0 = Profile|x64
		{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}.Release|x64.ActiveCfg = Release|x64
		{4FC34BC5-23C3-33F4-AC4E-8E15E9737F72}.Release|x64.Build.0 = Release|x64
		{24FA97E2-B236-39FA-8C96-947F1116F6D8}.Debug|x64.ActiveCfg = Debug|x64
		{24FA97E2-B236-39FA-8C96-947F1116F6D8}.Debug|x64.Build.0 = Debug|x64
		{24FA97E2-B236-39FA-8C96-947F1116F6D8}.Profile|x64.ActiveCfg = Profile|x64
		{24FA97E2-B236-39FA-8C96-947F1116F6D8}.Profile|x64.Build.0 = Profile|x64
		{24FA97E2-B236-39FA-8C96-947F1116F6D8}.Release|x64.ActiveCfg = Release|x64
		{24FA97E2-B236-39FA-8C96-947F1116F6D8}.Release|x64.Build.0 = Release|x64
		{B069737F-E4A2-3068-8C9F-3FA248062C58}.Debug|x64.ActiveCfg = Debug|x64
		{B069737F-E4A2-3068-8C9F-3FA248062C58}.Debug|x64.Build.0 = Debug|x64
		{B069737F-E4A2-3068-8C9F-3FA248062C58}.Profile|x64.ActiveCfg = Profile|x64
		{B069737F-E4A2-3068-8C9F-3FA248062C58}.Profile|x64.Build.0 = Profile|x64
		{B069737F-E4A2-3068-8C9F-3FA248062C58}.Release|x64.ActiveCfg = Release|x64
		{B069737F-E4A2-3068-8C9F-3FA248062C58}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FB3338A3-2959-35DF-A741-708D2208C234}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
