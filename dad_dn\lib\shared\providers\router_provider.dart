import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../features/prayer_tracker/presentation/pages/prayer_tracker_page.dart';
import '../../features/prayer_times/presentation/pages/prayer_times_page.dart';
import '../../features/quran/presentation/pages/quran_page.dart';
import '../../features/tasbih/presentation/pages/tasbih_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/community/presentation/pages/community_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';
import '../widgets/main_navigation.dart';

// Route names
class AppRoutes {
  static const String login = '/login';
  static const String register = '/register';
  static const String home = '/';
  static const String prayerTracker = '/prayer-tracker';
  static const String prayerTimes = '/prayer-times';
  static const String quran = '/quran';
  static const String tasbih = '/tasbih';
  static const String profile = '/profile';
  static const String community = '/community';
  static const String settings = '/settings';
}

final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.home,
    debugLogDiagnostics: true,
    routes: [
      // Auth Routes
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),
      
      // Main App Shell with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) {
          return MainNavigation(child: child);
        },
        routes: [
          GoRoute(
            path: AppRoutes.home,
            name: 'home',
            builder: (context, state) => const PrayerTrackerPage(),
          ),
          GoRoute(
            path: AppRoutes.prayerTracker,
            name: 'prayer-tracker',
            builder: (context, state) => const PrayerTrackerPage(),
          ),
          GoRoute(
            path: AppRoutes.prayerTimes,
            name: 'prayer-times',
            builder: (context, state) => const PrayerTimesPage(),
          ),
          GoRoute(
            path: AppRoutes.quran,
            name: 'quran',
            builder: (context, state) => const QuranPage(),
          ),
          GoRoute(
            path: AppRoutes.tasbih,
            name: 'tasbih',
            builder: (context, state) => const TasbihPage(),
          ),
          GoRoute(
            path: AppRoutes.community,
            name: 'community',
            builder: (context, state) => const CommunityPage(),
          ),
          GoRoute(
            path: AppRoutes.profile,
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
          GoRoute(
            path: AppRoutes.settings,
            name: 'settings',
            builder: (context, state) => const SettingsPage(),
          ),
        ],
      ),
    ],
    
    // Redirect logic for authentication
    redirect: (context, state) {
      // TODO: Implement authentication check
      // For now, allow access to all routes
      return null;
    },
    
    // Error handling
    errorBuilder: (context, state) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Page not found: ${state.location}',
                style: const TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go(AppRoutes.home),
                child: const Text('Go Home'),
              ),
            ],
          ),
        ),
      );
    },
  );
});
