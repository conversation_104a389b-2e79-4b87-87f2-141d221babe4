import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../providers/router_provider.dart';

class MainNavigation extends StatelessWidget {
  final Widget child;

  const MainNavigation({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _getCurrentIndex(context),
        onTap: (index) => _onItemTapped(context, index),
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.mosque),
            label: 'Prayer Tracker',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.access_time),
            label: 'Prayer Times',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.menu_book),
            label: 'Quran',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.radio_button_checked),
            label: 'Tasbih',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Community',
          ),
        ],
      ),
    );
  }

  int _getCurrentIndex(BuildContext context) {
    final String location = GoRouterState.of(context).location;
    
    switch (location) {
      case AppRoutes.home:
      case AppRoutes.prayerTracker:
        return 0;
      case AppRoutes.prayerTimes:
        return 1;
      case AppRoutes.quran:
        return 2;
      case AppRoutes.tasbih:
        return 3;
      case AppRoutes.community:
        return 4;
      default:
        return 0;
    }
  }

  void _onItemTapped(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go(AppRoutes.prayerTracker);
        break;
      case 1:
        context.go(AppRoutes.prayerTimes);
        break;
      case 2:
        context.go(AppRoutes.quran);
        break;
      case 3:
        context.go(AppRoutes.tasbih);
        break;
      case 4:
        context.go(AppRoutes.community);
        break;
    }
  }
}
