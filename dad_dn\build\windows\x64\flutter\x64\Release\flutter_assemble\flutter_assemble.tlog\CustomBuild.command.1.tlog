^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DAD\DAD_DN\BUILD\WINDOWS\X64\CMAKEFILES\C366BBC4C87D8F800CFB7BED17DCE865\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=C:\Users\<USER>\OneDrive\Desktop\dad\dad_dn FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\OneDrive\Desktop\dad\dad_dn\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\OneDrive\Desktop\dad\dad_dn FLUTTER_TARGET=C:\Users\<USER>\OneDrive\Desktop\dad\dad_dn\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\OneDrive\Desktop\dad\dad_dn\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DAD\DAD_DN\BUILD\WINDOWS\X64\CMAKEFILES\971141E10793DE2A417A755F2FFA7A67\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DAD\DAD_DN\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/dad/dad_dn/windows -BC:/Users/<USER>/OneDrive/Desktop/dad/dad_dn/build/windows/x64 --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/dad/dad_dn/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
