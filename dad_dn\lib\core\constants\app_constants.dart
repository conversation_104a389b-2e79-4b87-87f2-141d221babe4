class AppConstants {
  // App Info
  static const String appName = 'Dad Dn';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Prayer & Fasting Tracker App';

  // Prayer Times
  static const List<String> prayerNames = [
    'Fajr',
    '<PERSON>hu<PERSON>', 
    'Asr',
    'Maghrib',
    '<PERSON>ha'
  ];

  static const List<String> prayerNamesArabic = [
    'الفجر',
    'الظهر',
    'العصر',
    'المغرب',
    'العشاء'
  ];

  // Age of Religious Obligation
  static const int maleObligationAge = 12;
  static const int femaleObligationAge = 9;

  // Storage Keys
  static const String userProfileKey = 'user_profile';
  static const String prayerDataKey = 'prayer_data';
  static const String fastingDataKey = 'fasting_data';
  static const String settingsKey = 'app_settings';
  static const String tasbihDataKey = 'tasbih_data';
  static const String quranBookmarksKey = 'quran_bookmarks';

  // Notification IDs
  static const int fajrNotificationId = 1;
  static const int dhuhrNotificationId = 2;
  static const int asrNotificationId = 3;
  static const int maghribNotificationId = 4;
  static const int ishaNotificationId = 5;
  static const int dailyVerseNotificationId = 6;

  // Default Tasbih Counts
  static const int defaultTasbihCount = 33;
  static const int defaultTasbihMax = 99;

  // Common Dhikr
  static const List<String> commonDhikr = [
    'سبحان الله',
    'الحمد لله',
    'الله أكبر',
    'لا إله إلا الله',
    'استغفر الله',
  ];

  // API Endpoints (if needed)
  static const String baseUrl = 'https://api.example.com';
  
  // Calculation Methods for Prayer Times
  static const Map<String, String> calculationMethods = {
    'MWL': 'Muslim World League',
    'ISNA': 'Islamic Society of North America',
    'Egypt': 'Egyptian General Authority of Survey',
    'Makkah': 'Umm Al-Qura University, Makkah',
    'Karachi': 'University of Islamic Sciences, Karachi',
    'Tehran': 'Institute of Geophysics, University of Tehran',
  };
}
